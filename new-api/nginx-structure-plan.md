# Nginx结构化配置方案

## 目录结构

```
/root/workspace/shared/nginx/
├── nginx.conf                    # 主配置文件
├── conf.d/                       # 站点配置目录
│   ├── liangliangdamowang.edu.deal.conf  # 主域名配置
│   └── future-sites/             # 未来其他站点配置
├── includes/                     # 通用配置片段
│   ├── ssl-common.conf           # SSL通用配置
│   ├── security-headers.conf     # 安全头配置
│   ├── rate-limit.conf           # 限流配置
│   ├── access-control.conf       # 访问控制配置
│   └── proxy-common.conf         # 代理通用配置
├── ssl/                          # SSL证书目录
│   ├── liangliangdamowang.edu.deal/
│   │   ├── cert.pem
│   │   └── key.pem
│   └── dhparam.pem              # DH参数文件
├── logs/                         # 日志目录
│   ├── access/
│   └── error/
├── scripts/                      # 管理脚本
│   ├── deploy.sh                # 部署脚本
│   ├── reload.sh                # 重载脚本
│   └── backup.sh                # 备份脚本
└── docker-compose.yml           # Docker编排文件
```

## 配置特点

1. **模块化设计**：每个功能独立配置文件
2. **安全加固**：完整的SSL和安全头配置
3. **访问控制**：IP白名单、限流、防护
4. **易于扩展**：新项目只需添加新的conf文件
5. **统一管理**：所有配置集中在shared目录

## 实现优势

- ✅ 支持多个子路径项目
- ✅ 统一SSL证书管理
- ✅ 安全配置标准化
- ✅ 日志分离便于监控
- ✅ Docker化部署
- ✅ 自动化脚本管理

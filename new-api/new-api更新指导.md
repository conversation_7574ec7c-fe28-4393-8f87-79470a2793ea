# New-API 更新指导

本文档提供 New-API 项目的完整更新流程、补丁合并策略和自动化更新脚本，确保在更新过程中保持子路径配置和自定义修改。

## 1. 更新前准备

### 1.1 环境检查
```bash
cd /root/workspace/new-api

echo "=== 环境检查 ==="
echo "当前目录：$(pwd)"
echo "Git 状态："
git status --porcelain

echo "Docker 服务状态："
docker-compose ps

echo "当前版本信息："
git log --oneline -5

echo "磁盘空间："
df -h /root/workspace/
```

### 1.2 完整备份
```bash
# 创建带时间戳的备份目录
BACKUP_DIR="/root/workspace/backup/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

echo "=== 开始完整备份到 $BACKUP_DIR ==="

# 1. 备份数据库
echo "1. 备份数据库..."
if docker ps | grep -q mysql; then
    docker exec mysql mysqldump -uroot -p123456 new-api > $BACKUP_DIR/database_backup.sql
    echo "   数据库备份完成：$(ls -lh $BACKUP_DIR/database_backup.sql)"
else
    echo "   警告：MySQL 容器未运行，跳过数据库备份"
fi

# 2. 备份配置文件
echo "2. 备份配置文件..."
mkdir -p $BACKUP_DIR/nginx_conf
cp -r /root/workspace/shared/nginx/conf.d/ $BACKUP_DIR/nginx_conf/ 2>/dev/null || echo "   Nginx 配置不存在"
cp -r /root/workspace/shared/nginx/includes/ $BACKUP_DIR/nginx_conf/ 2>/dev/null || echo "   Nginx includes 不存在"

# 3. 备份应用数据
echo "3. 备份应用数据..."
cp -r /root/workspace/new-api/data/ $BACKUP_DIR/new-api_data/ 2>/dev/null || echo "   应用数据目录不存在"

# 4. 备份关键源码文件
echo "4. 备份关键源码文件..."
mkdir -p $BACKUP_DIR/source_files
cp /root/workspace/new-api/web/src/helpers/api.js $BACKUP_DIR/source_files/ 2>/dev/null || echo "   api.js 不存在"
cp /root/workspace/new-api/web/src/helpers/utils.js $BACKUP_DIR/source_files/ 2>/dev/null || echo "   utils.js 不存在"
cp /root/workspace/new-api/web/index.html $BACKUP_DIR/source_files/ 2>/dev/null || echo "   index.html 不存在"
cp /root/workspace/new-api/web/vite.config.js $BACKUP_DIR/source_files/ 2>/dev/null || echo "   vite.config.js 不存在"
cp /root/workspace/new-api/web/src/index.js $BACKUP_DIR/source_files/ 2>/dev/null || echo "   index.js 不存在"
cp /root/workspace/new-api/docker-compose.yml $BACKUP_DIR/source_files/ 2>/dev/null || echo "   docker-compose.yml 不存在"

# 5. 备份补丁文件
echo "5. 备份补丁文件..."
cp -r /root/workspace/new-api/patches/ $BACKUP_DIR/ 2>/dev/null || echo "   补丁目录不存在"

# 6. 记录当前状态
echo "6. 记录当前状态..."
git log --oneline -10 > $BACKUP_DIR/git_history.txt 2>/dev/null || echo "无法获取 Git 历史"
docker images | grep new-api > $BACKUP_DIR/docker_images.txt
docker-compose ps > $BACKUP_DIR/docker_status.txt 2>/dev/null || echo "无法获取 Docker 状态"

# 7. 记录当前配置
echo "7. 记录当前配置..."
echo "=== API 基础 URL 配置 ===" > $BACKUP_DIR/current_config.txt
grep -n "return.*ai" /root/workspace/new-api/web/src/helpers/api.js >> $BACKUP_DIR/current_config.txt 2>/dev/null || echo "API 配置未找到" >> $BACKUP_DIR/current_config.txt

echo "=== Logo 路径配置 ===" >> $BACKUP_DIR/current_config.txt
grep -n "/ai/logo.png" /root/workspace/new-api/web/src/helpers/utils.js >> $BACKUP_DIR/current_config.txt 2>/dev/null || echo "Logo 配置未找到" >> $BACKUP_DIR/current_config.txt

echo "=== HTML 模板配置 ===" >> $BACKUP_DIR/current_config.txt
grep -n "/ai/logo.png" /root/workspace/new-api/web/index.html >> $BACKUP_DIR/current_config.txt 2>/dev/null || echo "HTML 配置未找到" >> $BACKUP_DIR/current_config.txt

echo "=== Vite 配置 ===" >> $BACKUP_DIR/current_config.txt
grep -n "base.*ai" /root/workspace/new-api/web/vite.config.js >> $BACKUP_DIR/current_config.txt 2>/dev/null || echo "Vite 配置未找到" >> $BACKUP_DIR/current_config.txt

echo "=== React Router 配置 ===" >> $BACKUP_DIR/current_config.txt
grep -n 'basename.*ai' /root/workspace/new-api/web/src/index.js >> $BACKUP_DIR/current_config.txt 2>/dev/null || echo "Router 配置未找到" >> $BACKUP_DIR/current_config.txt

echo "备份完成！"
echo "备份目录：$BACKUP_DIR"
echo "备份内容："
ls -la $BACKUP_DIR/
echo "备份大小：$(du -sh $BACKUP_DIR | cut -f1)"
```

### 1.3 创建补丁文件系统
```bash
cd /root/workspace/new-api

# 创建补丁目录
mkdir -p patches

echo "=== 创建补丁文件 ==="

# 1. API 基础 URL 补丁
cat > patches/01-api-base-url.patch << 'EOF'
--- a/web/src/helpers/api.js
+++ b/web/src/helpers/api.js
@@ -5,12 +5,7 @@
 // 动态获取API基础URL
 function getApiBaseURL() {
   if (import.meta.env.VITE_REACT_APP_SERVER_URL) {
     return import.meta.env.VITE_REACT_APP_SERVER_URL;
   }

-  // 检测当前路径，如果在子路径下运行，使用相对路径
-  const currentPath = window.location.pathname;
-  if (currentPath.startsWith('/ai/')) {
-    return '/ai';
-  }
-
-  return '';
+  // 强制使用/ai作为基础路径，因为应用部署在/ai子路径下
+  return '/ai';
 }
EOF

# 2. Logo 路径补丁
cat > patches/02-logo-path.patch << 'EOF'
--- a/web/src/helpers/utils.js
+++ b/web/src/helpers/utils.js
@@ -32,7 +32,7 @@

 export function getLogo() {
   let logo = localStorage.getItem('logo');
-  if (!logo) return '/logo.png';
+  if (!logo) return '/ai/logo.png';
   return logo;
 }
EOF

# 3. HTML 模板补丁
cat > patches/03-html-template.patch << 'EOF'
--- a/web/index.html
+++ b/web/index.html
@@ -2,7 +2,7 @@
 <html lang="zh">
   <head>
     <meta charset="utf-8" />
-    <link rel="icon" href="/logo.png" />
+    <link rel="icon" href="/ai/logo.png" />
     <meta name="viewport" content="width=device-width, initial-scale=1" />
     <meta name="theme-color" content="#ffffff" />
     <meta
EOF

# 4. Vite 配置补丁
cat > patches/04-vite-config.patch << 'EOF'
--- a/web/vite.config.js
+++ b/web/vite.config.js
@@ -10,6 +10,7 @@

 export default defineConfig({
+  base: '/ai/',
   plugins: [
     react(),
     // 其他插件配置
EOF

# 5. React Router 补丁
cat > patches/05-react-router.patch << 'EOF'
--- a/web/src/index.js
+++ b/web/src/index.js
@@ -15,7 +15,7 @@
 const root = ReactDOM.createRoot(document.getElementById('root'));
 root.render(
   <React.StrictMode>
-    <BrowserRouter>
+    <BrowserRouter basename="/ai">
       <App />
     </BrowserRouter>
   </React.StrictMode>
EOF

# 6. Gemini API 端点补丁
cat > patches/06-gemini-api-endpoint.patch << 'EOF'
--- a/controller/channel.go
+++ b/controller/channel.go
@@ -171,7 +171,7 @@
 	url := fmt.Sprintf("%s/v1/models", baseURL)
 	switch channel.Type {
 	case constant.ChannelTypeGemini:
-		url = fmt.Sprintf("%s/v1beta/openai/models", baseURL)
+		url = fmt.Sprintf("%s/v1beta/models", baseURL)
 	case constant.ChannelTypeAli:
 		url = fmt.Sprintf("%s/compatible-mode/v1/models", baseURL)
 	}
EOF

echo "补丁文件创建完成："
ls -la patches/
```

## 2. 自动化更新流程

### 2.1 使用自动更新脚本
```bash
cd /root/workspace/new-api

# 运行自动更新脚本
./auto-update.sh

# 脚本将自动执行以下步骤：
# 1. 检查依赖和环境
# 2. 创建完整备份
# 3. 停止服务
# 4. 拉取最新代码
# 5. 应用子路径补丁
# 6. 验证配置
# 7. 重新构建和启动
# 8. 验证更新结果
```

### 2.2 快速修复常见错误
如果遇到静态资源404、重定向循环等问题：

```bash
cd /root/workspace/new-api

# 运行快速修复脚本
./quick-fix.sh

# 脚本将修复：
# 1. 静态资源路径问题
# 2. API 重定向循环
# 3. CSP 策略错误
# 4. MIME 类型错误
```

## 3. 手动更新流程

### 3.1 停止服务
```bash
cd /root/workspace/new-api
docker-compose down

cd /root/workspace/shared/nginx
docker-compose down
```

### 3.2 拉取最新代码
```bash
cd /root/workspace/new-api

# 检查当前状态
git status
git log --oneline -5

# 暂存本地更改（如果有）
git stash push -m "Update stash $(date)"

# 拉取最新代码
git fetch origin
git log --oneline HEAD..origin/main | head -10

# 合并更新
git merge origin/main

# 如果有冲突，解决后提交
# git add .
# git commit -m "解决合并冲突"
```

### 3.3 应用补丁
```bash
cd /root/workspace/new-api

echo "=== 应用补丁 ==="

# 方法1：使用补丁文件
if [ -d "patches" ]; then
    for patch in patches/*.patch; do
        echo "应用补丁: $patch"
        patch -p1 < "$patch" || echo "补丁 $patch 应用失败，可能需要手动处理"
    done
else
    echo "补丁目录不存在，使用手动修复方法"
fi

# 方法2：手动修复（如果补丁失败）
echo "=== 手动修复关键配置 ==="

# API 基础 URL
if [ -f "web/src/helpers/api.js" ]; then
    sed -i '/function getApiBaseURL/,/^}/c\
// 动态获取API基础URL\
function getApiBaseURL() {\
  if (import.meta.env.VITE_REACT_APP_SERVER_URL) {\
    return import.meta.env.VITE_REACT_APP_SERVER_URL;\
  }\
\
  // 强制使用/ai作为基础路径，因为应用部署在/ai子路径下\
  return '\''/ai'\'';\
}' web/src/helpers/api.js
    echo "✓ API 基础 URL 修复完成"
fi

# Logo 路径
if [ -f "web/src/helpers/utils.js" ]; then
    sed -i "s|return '/logo.png'|return '/ai/logo.png'|g" web/src/helpers/utils.js
    echo "✓ Logo 路径修复完成"
fi

# HTML 模板
if [ -f "web/index.html" ]; then
    sed -i 's|href="/logo.png"|href="/ai/logo.png"|g' web/index.html
    echo "✓ HTML 模板修复完成"
fi

# Vite 配置
if [ -f "web/vite.config.js" ]; then
    if ! grep -q "base: '/ai/'" web/vite.config.js; then
        sed -i '/export default defineConfig/a\  base: '\''/ai/'\'',' web/vite.config.js
        echo "✓ Vite 配置修复完成"
    fi
fi

# React Router 配置
if [ -f "web/src/index.js" ]; then
    if ! grep -q 'basename="/ai"' web/src/index.js; then
        sed -i 's|<BrowserRouter|<BrowserRouter basename="/ai"|g' web/src/index.js
        echo "✓ React Router 配置修复完成"
    fi
fi

# Gemini API 端点修复
if [ -f "controller/channel.go" ]; then
    if grep -q 'v1beta/openai/models' controller/channel.go; then
        sed -i 's|v1beta/openai/models|v1beta/models|g' controller/channel.go
        echo "✓ Gemini API 端点修复完成"
    else
        echo "✓ Gemini API 端点已正确"
    fi
fi
```

### 3.4 验证配置
```bash
cd /root/workspace/new-api

echo "=== 验证关键配置 ==="

# 检查 API 基础 URL
if grep -q "return '/ai'" web/src/helpers/api.js 2>/dev/null; then
    echo "✓ API 基础 URL 配置正确"
else
    echo "✗ API 基础 URL 配置错误"
    grep -A5 -B5 "getApiBaseURL" web/src/helpers/api.js || echo "函数未找到"
fi

# 检查 Logo 路径
if grep -q "/ai/logo.png" web/src/helpers/utils.js 2>/dev/null; then
    echo "✓ Logo 路径配置正确"
else
    echo "✗ Logo 路径配置错误"
    grep -n "logo.png" web/src/helpers/utils.js || echo "Logo 配置未找到"
fi

# 检查 HTML 模板
if grep -q "/ai/logo.png" web/index.html 2>/dev/null; then
    echo "✓ HTML 模板配置正确"
else
    echo "✗ HTML 模板配置错误"
    grep -n "logo.png" web/index.html || echo "HTML Logo 配置未找到"
fi

# 检查 Vite 配置
if grep -q "base: '/ai/'" web/vite.config.js 2>/dev/null; then
    echo "✓ Vite 配置正确"
else
    echo "✗ Vite 配置错误"
    grep -n "base:" web/vite.config.js || echo "Vite base 配置未找到"
fi

# 检查 React Router 配置
if grep -q 'basename="/ai"' web/src/index.js 2>/dev/null; then
    echo "✓ React Router 配置正确"
else
    echo "✗ React Router 配置错误"
    grep -n "BrowserRouter" web/src/index.js || echo "BrowserRouter 配置未找到"
fi

# 检查 Gemini API 端点
if grep -q 'v1beta/models' controller/channel.go 2>/dev/null && ! grep -q 'v1beta/openai/models' controller/channel.go 2>/dev/null; then
    echo "✓ Gemini API 端点配置正确"
else
    echo "✗ Gemini API 端点配置错误"
    grep -n "v1beta.*models" controller/channel.go || echo "Gemini 端点配置未找到"
fi

echo "=== 配置验证完成 ==="
```

### 3.5 重新构建和启动
```bash
cd /root/workspace/new-api

echo "=== 重新构建和启动服务 ==="

# 清理 Docker 缓存
echo "清理 Docker 缓存..."
docker system prune -f

# 重新构建（无缓存）
echo "重新构建 New-API..."
docker-compose build --no-cache --pull

# 启动 New-API
echo "启动 New-API..."
docker-compose up -d

# 等待服务启动
echo "等待 New-API 服务启动..."
sleep 20

# 检查 New-API 状态
echo "检查 New-API 状态..."
docker-compose ps

# 启动 Nginx
if [ -d "/root/workspace/shared/nginx" ]; then
    echo "启动 Nginx..."
    cd /root/workspace/shared/nginx
    docker-compose up -d
    sleep 10

    echo "检查 Nginx 状态..."
    docker-compose ps
fi

echo "=== 服务启动完成 ==="
```

### 3.6 验证更新结果
```bash
echo "=== 验证更新结果 ==="

# 等待服务完全启动
sleep 10

# 1. 测试主页访问
echo "1. 测试主页访问..."
HOMEPAGE_STATUS=$(curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ai/)
if [ "$HOMEPAGE_STATUS" = "200" ]; then
    echo "✓ 主页访问正常 (状态码: $HOMEPAGE_STATUS)"
else
    echo "✗ 主页访问失败 (状态码: $HOMEPAGE_STATUS)"
fi

# 2. 测试 API 状态
echo "2. 测试 API 状态..."
API_RESPONSE=$(curl -k -s https://localhost/ai/api/status 2>/dev/null)
if echo "$API_RESPONSE" | grep -q "success"; then
    echo "✓ API 状态正常"
    echo "   响应: $(echo "$API_RESPONSE" | jq -c '.' 2>/dev/null || echo "$API_RESPONSE")"
else
    echo "✗ API 状态异常"
    echo "   响应: $API_RESPONSE"
fi

# 3. 测试 Logo 图片
echo "3. 测试 Logo 图片..."
LOGO_STATUS=$(curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ai/logo.png)
if [ "$LOGO_STATUS" = "200" ]; then
    echo "✓ Logo 图片正常 (状态码: $LOGO_STATUS)"
else
    echo "✗ Logo 图片失败 (状态码: $LOGO_STATUS)"
fi

# 4. 测试静态资源
echo "4. 测试静态资源..."
HTML_CONTENT=$(curl -k -s https://localhost/ai/ 2>/dev/null)
if [ -n "$HTML_CONTENT" ]; then
    echo "HTML 内容获取成功，检查资源路径..."

    # 检查 JS 文件
    JS_FILES=$(echo "$HTML_CONTENT" | grep -o '/ai/assets/[^"]*\.js' | head -3)
    if [ -n "$JS_FILES" ]; then
        echo "找到 JS 文件:"
        echo "$JS_FILES" | while read js_file; do
            if [ -n "$js_file" ]; then
                JS_STATUS=$(curl -k -s -o /dev/null -w "%{http_code}" "https://localhost$js_file")
                if [ "$JS_STATUS" = "200" ]; then
                    echo "  ✓ $js_file (状态码: $JS_STATUS)"
                else
                    echo "  ✗ $js_file (状态码: $JS_STATUS)"
                fi
            fi
        done
    else
        echo "✗ 未找到 JS 文件路径"
    fi

    # 检查 CSS 文件
    CSS_FILES=$(echo "$HTML_CONTENT" | grep -o '/ai/assets/[^"]*\.css' | head -3)
    if [ -n "$CSS_FILES" ]; then
        echo "找到 CSS 文件:"
        echo "$CSS_FILES" | while read css_file; do
            if [ -n "$css_file" ]; then
                CSS_STATUS=$(curl -k -s -o /dev/null -w "%{http_code}" "https://localhost$css_file")
                if [ "$CSS_STATUS" = "200" ]; then
                    echo "  ✓ $css_file (状态码: $CSS_STATUS)"
                else
                    echo "  ✗ $css_file (状态码: $CSS_STATUS)"
                fi
            fi
        done
    else
        echo "✗ 未找到 CSS 文件路径"
    fi
else
    echo "✗ 无法获取 HTML 内容"
fi

# 5. 测试关键 API 端点
echo "5. 测试关键 API 端点..."
for endpoint in "notice" "home_page_content"; do
    echo "  测试 /ai/api/$endpoint..."
    ENDPOINT_STATUS=$(curl -k -s -o /dev/null -w "%{http_code}" "https://localhost/ai/api/$endpoint")
    if [[ "$ENDPOINT_STATUS" =~ ^(200|404)$ ]]; then
        echo "    ✓ 响应正常 (状态码: $ENDPOINT_STATUS)"
    else
        echo "    ✗ 响应异常 (状态码: $ENDPOINT_STATUS)"
    fi
done

# 6. 检查容器日志
echo "6. 检查容器日志..."
echo "New-API 最近日志:"
docker-compose -f /root/workspace/new-api/docker-compose.yml logs --tail=5 new-api 2>/dev/null || echo "无法获取 New-API 日志"

if [ -d "/root/workspace/shared/nginx" ]; then
    echo "Nginx 最近日志:"
    docker-compose -f /root/workspace/shared/nginx/docker-compose.yml logs --tail=5 nginx-proxy 2>/dev/null || echo "无法获取 Nginx 日志"
fi

echo "=== 验证完成 ==="
```

## 4. 故障排除

### 4.1 常见问题和解决方案

#### 问题1：静态资源404错误
```bash
# 症状
GET https://domain.com/assets/index-xxx.js 404 (Not Found)
Refused to apply style from 'https://domain.com/assets/xxx.css' because its MIME type ('text/html') is not a supported stylesheet MIME type

# 解决方案
cd /root/workspace/new-api
./quick-fix.sh

# 或手动修复
echo "检查 Vite 配置..."
grep "base:" web/vite.config.js
# 应该显示: base: '/ai/',

echo "检查 HTML 中的资源路径..."
curl -k -s https://localhost/ai/ | grep -E "(assets|href=|src=)" | head -5
# 所有路径都应该以 /ai/ 开头
```

#### 问题2：API重定向循环
```bash
# 症状
GET https://domain.com/api/status net::ERR_TOO_MANY_REDIRECTS

# 解决方案
echo "检查 API 基础 URL 配置..."
grep -A5 "getApiBaseURL" /root/workspace/new-api/web/src/helpers/api.js
# 应该返回 '/ai'

echo "检查 Nginx 配置..."
# 确保 location 块配置正确，避免重定向循环
```

#### 问题3：CSP策略错误
```bash
# 症状
Refused to load the image 'blob:https://domain.com/xxx' because it violates the following Content Security Policy directive

# 解决方案
# 在 Nginx 配置中添加 CSP 头，允许 blob URLs
# 参考 /tmp/csp-fix.conf 文件
```

### 4.2 回滚方案
如果更新失败，可以快速回滚：

```bash
# 查看可用备份
ls -la /root/workspace/backup/

# 选择备份目录进行回滚
BACKUP_DIR="/root/workspace/backup/20240126_143022"  # 替换为实际备份目录

cd /root/workspace/new-api

# 停止服务
docker-compose down
cd /root/workspace/shared/nginx && docker-compose down

# 恢复源码文件
cd /root/workspace/new-api
cp $BACKUP_DIR/source_files/api.js web/src/helpers/ 2>/dev/null || echo "api.js 恢复失败"
cp $BACKUP_DIR/source_files/utils.js web/src/helpers/ 2>/dev/null || echo "utils.js 恢复失败"
cp $BACKUP_DIR/source_files/index.html web/ 2>/dev/null || echo "index.html 恢复失败"
cp $BACKUP_DIR/source_files/vite.config.js web/ 2>/dev/null || echo "vite.config.js 恢复失败"
cp $BACKUP_DIR/source_files/index.js web/src/ 2>/dev/null || echo "index.js 恢复失败"

# 恢复数据库（如果需要）
if [ -f "$BACKUP_DIR/database_backup.sql" ]; then
    echo "恢复数据库..."
    docker-compose up -d mysql
    sleep 10
    docker exec -i mysql mysql -uroot -p123456 new-api < $BACKUP_DIR/database_backup.sql
fi

# 重新构建和启动
docker-compose build --no-cache
docker-compose up -d
sleep 15

cd /root/workspace/shared/nginx
docker-compose up -d

echo "回滚完成"
```

## 5. 自动化脚本说明

### 5.1 auto-update.sh - 完整更新脚本
```bash
# 功能：完整的自动更新流程
# 使用：./auto-update.sh
# 特点：
# - 自动备份
# - 拉取最新代码
# - 应用补丁
# - 验证配置
# - 重新构建
# - 测试验证

# 日志文件：/root/workspace/new-api/update.log
# 备份目录：/root/workspace/backup/YYYYMMDD_HHMMSS/
```

### 5.2 quick-fix.sh - 快速修复脚本
```bash
# 功能：快速修复常见错误
# 使用：./quick-fix.sh
# 适用场景：
# - 静态资源404错误
# - API重定向循环
# - CSP策略错误
# - MIME类型错误

# 修复内容：
# - 强制修复所有子路径配置
# - 重新构建和测试
# - 提供浏览器缓存清理提示
```

## 6. 最佳实践

### 6.1 更新前检查清单
- [ ] 确认当前服务运行正常
- [ ] 检查磁盘空间充足（至少2GB）
- [ ] 确认网络连接正常
- [ ] 通知用户即将进行维护
- [ ] 准备回滚计划

### 6.2 更新后验证清单
- [ ] 服务状态正常（docker-compose ps）
- [ ] 主页可以访问（https://domain.com/ai/）
- [ ] API 状态正常（/ai/api/status 返回 success: true）
- [ ] Logo 图片正常显示（/ai/logo.png 返回 200）
- [ ] 静态资源正常加载（JS、CSS 文件）
- [ ] 用户登录功能正常
- [ ] 数据库连接正常
- [ ] 关键功能测试通过

### 6.3 定期维护建议
```bash
# 每周执行
echo "=== 每周维护 ==="
# 1. 检查服务状态
docker-compose ps
# 2. 清理 Docker 缓存
docker system prune -f
# 3. 检查日志文件大小
du -sh /root/workspace/new-api/logs/ 2>/dev/null || echo "日志目录不存在"
# 4. 备份数据库
docker exec mysql mysqldump -uroot -p123456 new-api > /root/workspace/backup/weekly_$(date +%Y%m%d).sql

# 每月执行
echo "=== 每月维护 ==="
# 1. 检查更新
cd /root/workspace/new-api
git fetch origin
git log --oneline HEAD..origin/main | head -5
# 2. 清理旧备份（保留最近30天）
find /root/workspace/backup/ -type d -mtime +30 -exec rm -rf {} \; 2>/dev/null || true
# 3. 更新系统包
apt update && apt upgrade -y
```

## 7. 监控和告警

### 7.1 服务监控脚本
```bash
cat > /root/workspace/new-api/monitor.sh << 'EOF'
#!/bin/bash

# New-API 服务监控脚本

check_service() {
    local service_name="$1"
    local check_url="$2"

    if curl -k -s -o /dev/null -w "%{http_code}" "$check_url" | grep -q "200"; then
        echo "✓ $service_name 正常"
        return 0
    else
        echo "✗ $service_name 异常"
        return 1
    fi
}

echo "=== New-API 服务监控 $(date) ==="

# 检查主要服务
check_service "主页" "https://localhost/ai/"
check_service "API状态" "https://localhost/ai/api/status"
check_service "Logo图片" "https://localhost/ai/logo.png"

# 检查容器状态
echo "=== 容器状态 ==="
docker-compose -f /root/workspace/new-api/docker-compose.yml ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"

# 检查资源使用
echo "=== 资源使用 ==="
echo "内存使用："
free -h | grep Mem
echo "磁盘使用："
df -h /root/workspace/ | tail -1

echo "=== 监控完成 ==="
EOF

chmod +x /root/workspace/new-api/monitor.sh

# 设置定时监控（可选）
# echo "*/5 * * * * /root/workspace/new-api/monitor.sh >> /var/log/new-api-monitor.log 2>&1" | crontab -
```

### 7.2 日志分析
```bash
# 分析错误日志
echo "=== 错误日志分析 ==="
docker-compose -f /root/workspace/new-api/docker-compose.yml logs --since=1h new-api | grep -i error | tail -10

# 分析访问日志
echo "=== 访问日志分析 ==="
docker-compose -f /root/workspace/shared/nginx/docker-compose.yml logs --since=1h nginx-proxy | grep -E "(404|500)" | tail -10

# 分析性能
echo "=== 性能分析 ==="
curl -k -s -o /dev/null -w "主页响应时间: %{time_total}s\n" https://localhost/ai/
curl -k -s -o /dev/null -w "API响应时间: %{time_total}s\n" https://localhost/ai/api/status
```

## 8. 联系支持

### 8.1 问题报告模板
```
问题描述：
[详细描述遇到的问题]

错误信息：
[粘贴完整的错误信息]

环境信息：
- 操作系统：
- Docker版本：
- New-API版本：
- 浏览器：

重现步骤：
1.
2.
3.

已尝试的解决方案：
[列出已经尝试过的解决方法]

日志信息：
[粘贴相关的日志信息]
```

### 8.2 常用命令速查
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f new-api

# 重启服务
docker-compose restart

# 重新构建
docker-compose build --no-cache

# 进入容器
docker-compose exec new-api bash

# 查看配置
grep -r "base.*ai" web/
grep -r "return.*ai" web/src/helpers/

# 测试连接
curl -k -I https://localhost/ai/
curl -k -s https://localhost/ai/api/status | jq '.'

# 清理缓存
docker system prune -f
```

## 9. 总结

本更新指导提供了完整的 New-API 更新流程，包括：

1. **自动化更新**：使用 `auto-update.sh` 进行一键更新
2. **快速修复**：使用 `quick-fix.sh` 解决常见问题
3. **手动更新**：详细的手动更新步骤
4. **故障排除**：常见问题的解决方案
5. **回滚方案**：更新失败时的恢复方法
6. **监控维护**：定期维护和监控建议

关键要点：
- 始终在更新前创建完整备份
- 确保子路径配置（`/ai/`）在更新后保持正确
- 验证所有关键功能正常工作
- 保留多个版本的备份以便回滚
- 定期监控服务状态和性能

如果在更新过程中遇到问题，请：
1. 查看错误日志
2. 运行快速修复脚本
3. 检查配置文件
4. 必要时执行回滚操作

### 2. 记录当前修改

```bash
cd /root/workspace/new-api

# 查看当前状态
git status

# 查看关键文件的修改
echo "=== API 基础 URL 配置 ==="
grep -n "return '/ai'" web/src/helpers/api.js

echo "=== Logo 路径配置 ==="
grep -n "/ai/logo.png" web/src/helpers/utils.js

echo "=== HTML 模板配置 ==="
grep -n "/ai/logo.png" web/index.html

echo "=== Vite 配置 ==="
grep -n "base: '/ai/'" web/vite.config.js

echo "=== React Router 配置 ==="
grep -n 'basename="/ai"' web/src/index.js
```

### 3. 创建自定义补丁文件

```bash
# 创建补丁目录
mkdir -p /root/workspace/new-api/patches

# 创建 API 基础 URL 补丁
cat > /root/workspace/new-api/patches/api-base-url.patch << 'EOF'
--- a/web/src/helpers/api.js
+++ b/web/src/helpers/api.js
@@ -5,12 +5,7 @@
 // 动态获取API基础URL
 function getApiBaseURL() {
   if (import.meta.env.VITE_REACT_APP_SERVER_URL) {
     return import.meta.env.VITE_REACT_APP_SERVER_URL;
   }

-  // 检测当前路径，如果在子路径下运行，使用相对路径
-  const currentPath = window.location.pathname;
-  if (currentPath.startsWith('/ai/')) {
-    return '/ai';
-  }
-
-  return '';
+  // 强制使用/ai作为基础路径，因为应用部署在/ai子路径下
+  return '/ai';
 }
EOF

# 创建 Logo 路径补丁
cat > /root/workspace/new-api/patches/logo-path.patch << 'EOF'
--- a/web/src/helpers/utils.js
+++ b/web/src/helpers/utils.js
@@ -32,7 +32,7 @@

 export function getLogo() {
   let logo = localStorage.getItem('logo');
-  if (!logo) return '/logo.png';
+  if (!logo) return '/ai/logo.png';
   return logo;
 }
EOF

# 创建 HTML 模板补丁
cat > /root/workspace/new-api/patches/html-template.patch << 'EOF'
--- a/web/index.html
+++ b/web/index.html
@@ -2,7 +2,7 @@
 <html lang="zh">
   <head>
     <meta charset="utf-8" />
-    <link rel="icon" href="/logo.png" />
+    <link rel="icon" href="/ai/logo.png" />
     <meta name="viewport" content="width=device-width, initial-scale=1" />
     <meta name="theme-color" content="#ffffff" />
     <meta
EOF
```

## 更新步骤

### 1. 停止服务

```bash
cd /root/workspace/new-api
docker-compose down

cd /root/workspace/shared/nginx
docker-compose down
```

### 2. 获取最新代码

```bash
cd /root/workspace/new-api

# 保存当前分支
CURRENT_BRANCH=$(git branch --show-current)
echo "当前分支：$CURRENT_BRANCH"

# 获取最新代码
git fetch origin

# 查看可用的更新
git log --oneline HEAD..origin/main | head -10

# 确认更新
read -p "是否继续更新？(y/N): " confirm
if [[ $confirm != [yY] ]]; then
    echo "更新已取消"
    exit 1
fi

# 合并最新代码
git merge origin/main
```

### 3. 重新应用关键修改

```bash
cd /root/workspace/new-api

# 应用补丁
echo "应用 API 基础 URL 补丁..."
patch -p1 < patches/api-base-url.patch

echo "应用 Logo 路径补丁..."
patch -p1 < patches/logo-path.patch

echo "应用 HTML 模板补丁..."
patch -p1 < patches/html-template.patch

# 手动检查和修复冲突（如果有）
echo "检查关键配置..."
grep -n "return '/ai'" web/src/helpers/api.js || echo "警告：API 基础 URL 配置可能需要手动修复"
grep -n "/ai/logo.png" web/src/helpers/utils.js || echo "警告：Logo 路径配置可能需要手动修复"
grep -n "/ai/logo.png" web/index.html || echo "警告：HTML 模板配置可能需要手动修复"
```

### 4. 验证配置文件

```bash
# 检查 Vite 配置
echo "检查 Vite 配置..."
if ! grep -q "base: '/ai/'" web/vite.config.js; then
    echo "修复 Vite 配置..."
    sed -i "s/base: '[^']*'/base: '\/ai\/'/" web/vite.config.js
fi

# 检查 React Router 配置
echo "检查 React Router 配置..."
if ! grep -q 'basename="/ai"' web/src/index.js; then
    echo "修复 React Router 配置..."
    sed -i 's/basename="[^"]*"/basename="\/ai"/' web/src/index.js
fi

# 最终验证
echo "=== 最终配置验证 ==="
echo "API 基础 URL："
grep -n "return '/ai'" web/src/helpers/api.js

echo "Logo 路径："
grep -n "/ai/logo.png" web/src/helpers/utils.js

echo "HTML 模板："
grep -n "/ai/logo.png" web/index.html

echo "Vite 配置："
grep -n "base: '/ai/'" web/vite.config.js

echo "React Router 配置："
grep -n 'basename="/ai"' web/src/index.js
```

### 5. 重新构建和部署

```bash
cd /root/workspace/new-api

# 重新构建（无缓存）
echo "重新构建 New-API..."
docker-compose build --no-cache

# 启动服务
echo "启动 New-API 服务..."
docker-compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 15

# 启动 Nginx
cd /root/workspace/shared/nginx
echo "启动 Nginx 服务..."
docker-compose up -d

# 等待 Nginx 启动
sleep 5
```

### 6. 验证更新

```bash
echo "=== 验证更新结果 ==="

# 检查服务状态
echo "1. 检查服务状态："
docker-compose -f /root/workspace/new-api/docker-compose.yml ps
docker-compose -f /root/workspace/shared/nginx/docker-compose.yml ps

# 测试主要功能
echo "2. 测试主页访问："
curl -k -s -o /dev/null -w "状态码: %{http_code}, 响应时间: %{time_total}s\n" https://localhost/ai/

echo "3. 测试 API 状态："
curl -k -s https://localhost/ai/api/status | jq '.success' 2>/dev/null || echo "API 测试失败"

echo "4. 测试 Logo 图片："
curl -k -s -o /dev/null -w "状态码: %{http_code}, 响应时间: %{time_total}s\n" https://localhost/ai/logo.png

echo "5. 测试静态资源："
# 获取当前 HTML 中的 JS 文件名
JS_FILE=$(curl -k -s https://localhost/ai/ | grep -o '/ai/assets/index-[^"]*\.js' | head -1)
if [ -n "$JS_FILE" ]; then
    curl -k -s -o /dev/null -w "JS文件 $JS_FILE: %{http_code}, 响应时间: %{time_total}s\n" https://localhost$JS_FILE
else
    echo "警告：无法找到 JS 文件"
fi

echo "6. 测试关键 API 端点："
for endpoint in "status" "notice" "home_page_content"; do
  echo "  测试 /ai/api/$endpoint:"
  curl -k -s -o /dev/null -w "    状态码: %{http_code}, 响应时间: %{time_total}s\n" https://localhost/ai/api/$endpoint
done
```

## 关键文件保护清单

以下文件包含重要的子路径配置，更新时必须保持：

### 前端配置文件

1. **`web/src/helpers/api.js`** - API 基础 URL 配置
   ```javascript
   // 必须保持
   return '/ai';
   ```

2. **`web/src/helpers/utils.js`** - Logo 路径配置
   ```javascript
   // 必须保持
   if (!logo) return '/ai/logo.png';
   ```

3. **`web/index.html`** - HTML 模板配置
   ```html
   <!-- 必须保持 -->
   <link rel="icon" href="/ai/logo.png" />
   ```

4. **`web/vite.config.js`** - Vite 基础路径配置
   ```javascript
   // 必须保持
   base: '/ai/',
   ```

5. **`web/src/index.js`** - React Router basename 配置
   ```javascript
   // 必须保持
   basename="/ai"
   ```

### Nginx 配置文件

1. **`/root/workspace/shared/nginx/conf.d/*.conf`** - 站点配置
2. **`/root/workspace/shared/nginx/includes/*.conf`** - 包含文件
3. **`/root/workspace/shared/nginx/nginx.conf`** - 主配置

## 自动化更新脚本

创建自动化更新脚本：

```bash
cat > /root/workspace/new-api/update-new-api.sh << 'EOF'
#!/bin/bash

set -e

echo "=== New-API 自动更新脚本 ==="

# 配置
BACKUP_DIR="/root/workspace/backup/$(date +%Y%m%d_%H%M%S)"
NEW_API_DIR="/root/workspace/new-api"
NGINX_DIR="/root/workspace/shared/nginx"

# 创建备份
echo "1. 创建备份..."
mkdir -p $BACKUP_DIR
docker exec mysql mysqldump -uroot -p123456 new-api > $BACKUP_DIR/database_backup.sql
cp -r $NGINX_DIR/conf.d/ $BACKUP_DIR/nginx_conf/
cp -r $NEW_API_DIR/data/ $BACKUP_DIR/new-api_data/

# 备份关键源码文件
mkdir -p $BACKUP_DIR/source_files
cp $NEW_API_DIR/web/src/helpers/api.js $BACKUP_DIR/source_files/
cp $NEW_API_DIR/web/src/helpers/utils.js $BACKUP_DIR/source_files/
cp $NEW_API_DIR/web/index.html $BACKUP_DIR/source_files/

echo "备份完成：$BACKUP_DIR"

# 停止服务
echo "2. 停止服务..."
cd $NEW_API_DIR && docker-compose down
cd $NGINX_DIR && docker-compose down

# 更新代码
echo "3. 更新代码..."
cd $NEW_API_DIR
git fetch origin
git merge origin/main

# 应用补丁
echo "4. 应用自定义配置..."
# API 基础 URL
sed -i 's/return '\'''\'';//g' web/src/helpers/api.js
sed -i '/检测当前路径/,/return '\'''\''/{//!d}' web/src/helpers/api.js
sed -i 's/检测当前路径.*/强制使用\/ai作为基础路径，因为应用部署在\/ai子路径下/' web/src/helpers/api.js
sed -i 's/return '\'''\'';//g' web/src/helpers/api.js
sed -i '/强制使用/a\  return '\''/ai'\'';' web/src/helpers/api.js

# Logo 路径
sed -i "s|return '/logo.png'|return '/ai/logo.png'|g" web/src/helpers/utils.js

# HTML 模板
sed -i 's|href="/logo.png"|href="/ai/logo.png"|g' web/index.html

# 重新构建和启动
echo "5. 重新构建和启动..."
docker-compose build --no-cache
docker-compose up -d

sleep 15

cd $NGINX_DIR
docker-compose up -d

sleep 5

# 验证
echo "6. 验证更新..."
curl -k -s https://localhost/ai/api/status | jq '.success' > /dev/null && echo "✓ API 测试通过" || echo "✗ API 测试失败"
curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ai/logo.png | grep -q "200" && echo "✓ Logo 测试通过" || echo "✗ Logo 测试失败"

echo "=== 更新完成 ==="
echo "备份目录：$BACKUP_DIR"
EOF

chmod +x /root/workspace/new-api/update-new-api.sh
```

## 回滚方案

如果更新出现问题，可以快速回滚：

### 1. 快速回滚脚本

```bash
cat > /root/workspace/new-api/rollback-new-api.sh << 'EOF'
#!/bin/bash

if [ -z "$1" ]; then
    echo "用法: $0 <备份目录>"
    echo "可用备份："
    ls -la /root/workspace/backup/
    exit 1
fi

BACKUP_DIR="$1"
NEW_API_DIR="/root/workspace/new-api"
NGINX_DIR="/root/workspace/shared/nginx"

echo "=== 回滚到备份：$BACKUP_DIR ==="

# 停止服务
echo "1. 停止服务..."
cd $NEW_API_DIR && docker-compose down
cd $NGINX_DIR && docker-compose down

# 恢复源码文件
echo "2. 恢复源码文件..."
cp $BACKUP_DIR/source_files/api.js $NEW_API_DIR/web/src/helpers/
cp $BACKUP_DIR/source_files/utils.js $NEW_API_DIR/web/src/helpers/
cp $BACKUP_DIR/source_files/index.html $NEW_API_DIR/web/

# 恢复配置文件
echo "3. 恢复配置文件..."
cp -r $BACKUP_DIR/nginx_conf/* $NGINX_DIR/conf.d/
cp -r $BACKUP_DIR/new-api_data/* $NEW_API_DIR/data/

# 恢复数据库
echo "4. 恢复数据库..."
cd $NEW_API_DIR && docker-compose up -d mysql
sleep 10
docker exec -i mysql mysql -uroot -p123456 new-api < $BACKUP_DIR/database_backup.sql

# 重新构建和启动
echo "5. 重新构建和启动..."
docker-compose build --no-cache
docker-compose up -d

sleep 15

cd $NGINX_DIR
docker-compose up -d

echo "=== 回滚完成 ==="
EOF

chmod +x /root/workspace/new-api/rollback-new-api.sh
```

### 2. 手动回滚步骤

```bash
# 停止服务
cd /root/workspace/new-api && docker-compose down
cd /root/workspace/shared/nginx && docker-compose down

# 回滚代码到特定提交
cd /root/workspace/new-api
git log --oneline -10  # 查看最近的提交
git reset --hard <commit_hash>  # 回滚到指定提交

# 重新应用关键修改
# ... (重复上述修改步骤)

# 重新构建
docker-compose build --no-cache
docker-compose up -d

# 恢复数据库（如果需要）
docker exec -i mysql mysql -uroot -p123456 new-api < /path/to/backup.sql
```

## 更新检查清单

更新完成后，请检查以下项目：

- [ ] 服务状态正常（docker-compose ps）
- [ ] 主页可以访问（https://domain.com/ai/）
- [ ] API 状态正常（/ai/api/status 返回 success: true）
- [ ] Logo 图片正常显示（/ai/logo.png 返回 200）
- [ ] 静态资源正常加载（JS、CSS 文件）
- [ ] 用户登录功能正常
- [ ] 数据库连接正常
- [ ] 日志记录正常

## 常见更新问题

### 1. 合并冲突

```bash
# 查看冲突文件
git status

# 手动解决冲突后
git add .
git commit -m "解决合并冲突"
```

### 2. 构建失败

```bash
# 清理构建缓存
docker system prune -f
docker-compose build --no-cache --pull
```

### 3. 服务启动失败

```bash
# 查看日志
docker-compose logs new-api
docker-compose logs nginx

# 检查端口占用
netstat -tlnp | grep :3000
```

## 最佳实践

1. **定期更新**：建议每月检查一次更新
2. **测试环境**：先在测试环境验证更新
3. **备份策略**：保留最近 3 个版本的备份
4. **监控告警**：设置服务监控和告警
5. **文档更新**：及时更新部署文档

## 联系支持

如果在更新过程中遇到问题，请：

1. 查看日志文件
2. 检查配置文件
3. 尝试回滚到上一个版本
4. 参考错误指南文档
# New-API 子路径部署指南（详细版）

## 概述

本指南详细说明如何将 New-API 部署在子路径 `/ai/` 下，通过 nginx 反向代理实现 HTTPS 访问。本部署方案基于源码修改和自定义构建，与直接使用官方镜像不同。

## 架构说明

```
用户请求 → nginx (HTTPS) → new-api 容器 (HTTP:3000)
https://domain.com/ai/ → http://new-api:3000/
```

## 前置要求

- Docker 和 Docker Compose
- SSL 证书文件
- 域名解析配置

## 目录结构

```
/root/workspace/
├── new-api/                    # New-API 项目目录
│   ├── web/                    # 前端源码
│   ├── docker-compose.yml     # 服务编排配置
│   ├── Dockerfile             # 构建配置
│   └── data/                  # 数据目录
└── shared/
    └── nginx/                 # Nginx 配置
        ├── docker-compose.yml
        ├── nginx.conf
        ├── conf.d/
        ├── ssl/              # SSL 证书目录
        └── logs/             # 日志目录
```

## 第一步：准备 SSL 证书

```bash
# 创建 SSL 证书目录
mkdir -p /root/workspace/shared/nginx/ssl

# 复制证书文件（替换为您的实际证书）
cp certificate.crt /root/workspace/shared/nginx/ssl/
cp private.key /root/workspace/shared/nginx/ssl/

# 设置正确权限
chmod 644 /root/workspace/shared/nginx/ssl/certificate.crt
chmod 600 /root/workspace/shared/nginx/ssl/private.key
```

## 第二步：修改前端源码（关键步骤）

### 1. 修改 API 基础 URL (`new-api/web/src/helpers/api.js`)

```javascript
// 动态获取API基础URL
function getApiBaseURL() {
  if (import.meta.env.VITE_REACT_APP_SERVER_URL) {
    return import.meta.env.VITE_REACT_APP_SERVER_URL;
  }

  // 强制使用/ai作为基础路径，因为应用部署在/ai子路径下
  return '/ai';
}
```

### 2. 修改 Logo 路径 (`new-api/web/src/helpers/utils.js`)

```javascript
export function getLogo() {
  let logo = localStorage.getItem('logo');
  if (!logo) return '/ai/logo.png';  // 修改默认路径
  return logo;
}
```

### 3. 修改 HTML 模板 (`new-api/web/index.html`)

```html
<link rel="icon" href="/ai/logo.png" />  <!-- 修改favicon路径 -->
```

### 4. 确认 Vite 配置 (`new-api/web/vite.config.js`)

```javascript
export default defineConfig({
  base: '/ai/',  // 确保设置正确的基础路径
  // ... 其他配置
});
```

### 5. 确认 React Router 配置 (`new-api/web/src/index.js`)

```javascript
<BrowserRouter
  basename="/ai"
  future={{
    v7_startTransition: true,
    v7_relativeSplatPath: true,
  }}
>
```

## 第三步：配置 Nginx

### 主配置文件 (`shared/nginx/nginx.conf`)

```nginx
user nginx;
worker_processes auto;
error_log /var/log/nginx/error/error.log warn;
pid /var/run/nginx.pid;

worker_rlimit_nofile 65535;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    charset utf-8;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';
    
    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain text/css text/xml text/javascript
        application/json application/javascript
        application/xml+rss application/atom+xml
        image/svg+xml;
    
    server_tokens off;
    
    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
}
```

### 站点配置文件 (`shared/nginx/conf.d/liangliangdamowang.edu.deal.conf`)

```nginx
# upstream 定义
upstream new-api {
    server new-api:3000;
    keepalive 32;
}

# 限流配置
limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

# WebSocket 支持
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name liangliangdamowang.edu.deal;
    access_log /var/log/nginx/access/liangliangdamowang.edu.deal-http.log main;
    return 301 https://$server_name$request_uri;
}

# HTTPS 主服务器
server {
    listen 443 ssl;
    http2 on;
    server_name liangliangdamowang.edu.deal;
    
    # SSL 证书配置
    ssl_certificate /etc/nginx/ssl/certificate.crt;
    ssl_certificate_key /etc/nginx/ssl/private.key;
    
    # 包含配置文件
    include /etc/nginx/includes/ssl-common.conf;
    include /etc/nginx/includes/security-headers.conf;
    include /etc/nginx/includes/access-control.conf;
    
    # 日志配置
    access_log /var/log/nginx/access/liangliangdamowang.edu.deal.log main;
    error_log /var/log/nginx/error/liangliangdamowang.edu.deal.log warn;
    
    # 根路径
    location / {
        root /var/www/html;
        index index.html index.htm;
        try_files $uri $uri/ =404;
        limit_req zone=general burst=20 nodelay;
        limit_conn conn_limit_per_ip 20;
    }
    
    # 特殊处理登录接口 - 最高优先级
    location /ai/api/user/login {
        include /etc/nginx/includes/proxy-common.conf;
        rewrite ^/ai/(.*)$ /$1 break;
        proxy_pass http://new-api;
        proxy_set_header X-Forwarded-Prefix /ai;
        limit_req zone=login burst=3 nodelay;
    }

    # 特殊处理静态资源
    location ~* /ai/.*\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        include /etc/nginx/includes/proxy-common.conf;
        rewrite ^/ai/(.*)$ /$1 break;
        proxy_pass http://new-api;
        proxy_set_header X-Forwarded-Prefix /ai;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }

    # New-API 路径配置 - 核心子路径访问
    location /ai/ {
        include /etc/nginx/includes/proxy-common.conf;
        rewrite ^/ai/(.*)$ /$1 break;
        proxy_pass http://new-api/;
        proxy_set_header X-Forwarded-Prefix /ai;
        limit_req zone=api burst=10 nodelay;
        limit_conn conn_limit_per_ip 10;
    }
    
    # 处理 /ai 重定向
    location = /ai {
        return 301 /ai/;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

## 第四步：创建 Nginx 包含文件

### SSL 通用配置 (`shared/nginx/includes/ssl-common.conf`)

```nginx
# SSL 协议和加密套件
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
ssl_prefer_server_ciphers off;

# SSL 会话缓存
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;

# SSL 性能优化
ssl_buffer_size 8k;
```

### 安全头配置 (`shared/nginx/includes/security-headers.conf`)

```nginx
# HSTS - 强制 HTTPS
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

# 防止点击劫持
add_header X-Frame-Options "SAMEORIGIN" always;

# 防止 MIME 类型嗅探
add_header X-Content-Type-Options "nosniff" always;

# XSS 保护
add_header X-XSS-Protection "1; mode=block" always;

# 引用策略
add_header Referrer-Policy "strict-origin-when-cross-origin" always;

# 权限策略
add_header Permissions-Policy "camera=(), microphone=(), geolocation=()" always;

# CSP 内容安全策略（支持 blob URL）
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: blob:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'self';" always;
```

### 代理通用配置 (`shared/nginx/includes/proxy-common.conf`)

```nginx
# 代理头设置
proxy_set_header Host $host;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_set_header X-Forwarded-Host $server_name;
proxy_set_header X-Forwarded-Port $server_port;

# WebSocket 支持
proxy_http_version 1.1;
proxy_set_header Upgrade $http_upgrade;
proxy_set_header Connection $connection_upgrade;

# 超时设置
proxy_connect_timeout 60s;
proxy_send_timeout 60s;
proxy_read_timeout 60s;

# 缓冲设置
proxy_buffering off;
proxy_request_buffering off;
proxy_buffer_size 4k;
proxy_buffers 8 4k;

# 重试设置
proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
proxy_next_upstream_tries 3;
proxy_next_upstream_timeout 10s;

# 隐藏后端服务器信息
proxy_hide_header X-Powered-By;
proxy_hide_header Server;
```

### 访问控制配置 (`shared/nginx/includes/access-control.conf`)

```nginx
# 基本访问控制
# 可以在这里添加 IP 白名单或黑名单
# allow ***********/24;
# deny all;

# 防止访问敏感文件
location ~ /\. {
    deny all;
    access_log off;
    log_not_found off;
}

location ~ ~$ {
    deny all;
    access_log off;
    log_not_found off;
}
```

## 第五步：配置 Docker Compose

### New-API 服务配置 (`new-api/docker-compose.yml`)

```yaml
version: '3.4'

services:
  new-api:
    build: .
    container_name: new-api
    restart: always
    command: --log-dir /app/logs
    ports:
      - "3000:3000"
    volumes:
      - ./data:/data
      - ./logs:/app/logs
    environment:
      - SQL_DSN=root:123456@tcp(mysql:3306)/new-api
      - REDIS_CONN_STRING=redis://redis
      - TZ=Asia/Shanghai
      - ERROR_LOG_ENABLED=true
    depends_on:
      - redis
      - mysql
    healthcheck:
      test: ["CMD-SHELL", "wget -q -O - http://localhost:3000/api/status | grep -o '\"success\":\\s*true' | awk -F: '{print $$2}'"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:latest
    container_name: redis
    restart: always

  mysql:
    image: mysql:8.2
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: new-api
    volumes:
      - mysql_data:/var/lib/mysql

volumes:
  mysql_data:
```

### Nginx 服务配置 (`shared/nginx/docker-compose.yml`)

```yaml
version: '3.4'

services:
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./conf.d:/etc/nginx/conf.d:ro
      - ./includes:/etc/nginx/includes:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/access:/var/log/nginx/access
      - ./logs/error:/var/log/nginx/error
      - /var/www/html:/var/www/html:ro
    external_links:
      - new-api:new-api
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
```

## 第六步：部署步骤

### 1. 创建目录结构

```bash
# 创建必要目录
mkdir -p /root/workspace/shared/nginx/{conf.d,includes,ssl,logs/{access,error}}
mkdir -p /root/workspace/new-api/{data,logs}

# 设置权限
chmod 755 /root/workspace/shared/nginx/logs/{access,error}
chmod 755 /root/workspace/new-api/logs
```

### 2. 启动 New-API 服务

```bash
cd /root/workspace/new-api

# 构建并启动服务
docker-compose build --no-cache
docker-compose up -d

# 检查服务状态
docker-compose ps
docker-compose logs -f new-api
```

### 3. 启动 Nginx 服务

```bash
cd /root/workspace/shared/nginx

# 启动 nginx
docker-compose up -d

# 检查配置
docker-compose exec nginx nginx -t

# 检查服务状态
docker-compose ps
```

### 4. 验证部署

```bash
# 测试 HTTPS 访问
curl -k -I https://localhost/ai/

# 测试 API 接口
curl -k -s https://localhost/ai/api/status | jq '.success'

# 测试静态资源
curl -k -I https://localhost/ai/logo.png

# 测试所有关键端点
for endpoint in "status" "notice" "home_page_content"; do
  echo "测试 /ai/api/$endpoint:"
  curl -k -s -o /dev/null -w "  状态码: %{http_code}, 响应时间: %{time_total}s\n" https://localhost/ai/api/$endpoint
done
```

## 第七步：网络配置

### Docker 网络连接

```bash
# 创建共享网络（如果需要）
docker network create new-api-network

# 连接 nginx 到 new-api 网络
docker network connect new-api-network nginx-proxy
```

## 重要修改说明

### 前端代码修改要点

1. **API 基础 URL 强制设置**：
   - 原始代码使用动态检测，可能在某些情况下失效
   - 修改为强制返回 `/ai`，确保所有 API 请求都使用正确前缀

2. **Logo 路径修复**：
   - 原始默认路径为 `/logo.png`，导致 404 错误
   - 修改为 `/ai/logo.png`，确保在子路径下正确访问

3. **HTML 模板修复**：
   - favicon 路径也需要使用 `/ai/` 前缀
   - 确保浏览器标签页图标正确显示

### Nginx 配置优化要点

1. **Location 块顺序**：
   - 避免嵌套 location 配置，会导致路由匹配错误
   - 按优先级排序：登录接口 → 静态资源 → 通用 API

2. **静态资源缓存**：
   - 设置适当的缓存策略，提高性能
   - 支持各种静态文件类型

3. **安全头配置**：
   - 包含完整的安全头设置
   - CSP 配置支持 blob URL，避免图片加载问题

## 故障排除

### 常见问题及解决方案

1. **404 错误**：
   - 检查 nginx location 配置顺序
   - 确认前端构建配置正确
   - 验证 API 基础 URL 设置

2. **静态资源加载失败**：
   - 确认 vite.config.js 中的 base 路径设置
   - 检查 nginx 静态资源处理配置
   - 验证文件路径是否正确

3. **API 调用失败**：
   - 检查代理配置和网络连接
   - 验证容器间通信
   - 查看服务日志

4. **SSL 证书问题**：
   - 验证证书文件路径和权限
   - 检查证书有效期
   - 确认域名匹配

### 日志查看

```bash
# Nginx 日志
docker-compose -f /root/workspace/shared/nginx/docker-compose.yml logs nginx

# New-API 日志
docker-compose -f /root/workspace/new-api/docker-compose.yml logs new-api

# 系统日志
tail -f /root/workspace/shared/nginx/logs/error/error.log
tail -f /root/workspace/new-api/logs/oneapi-*.log
```

### 调试命令

```bash
# 检查容器状态
docker ps -a

# 检查网络连接
docker network ls
docker network inspect bridge

# 测试容器间通信
docker exec nginx-proxy ping new-api

# 检查端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :443
netstat -tlnp | grep :3000
```

## 维护建议

### 定期维护任务

1. **备份数据库和配置文件**：
   ```bash
   # 备份数据库
   docker exec mysql mysqldump -uroot -p123456 new-api > backup_$(date +%Y%m%d).sql

   # 备份配置文件
   tar -czf config_backup_$(date +%Y%m%d).tar.gz /root/workspace/shared/nginx/conf.d/
   ```

2. **监控日志文件大小**：
   ```bash
   # 清理旧日志
   find /root/workspace/shared/nginx/logs/ -name "*.log" -mtime +30 -delete
   find /root/workspace/new-api/logs/ -name "*.log" -mtime +30 -delete
   ```

3. **定期更新 SSL 证书**：
   ```bash
   # 检查证书有效期
   openssl x509 -in /root/workspace/shared/nginx/ssl/certificate.crt -text -noout | grep "Not After"
   ```

4. **监控服务健康状态**：
   ```bash
   # 检查服务状态
   docker-compose -f /root/workspace/new-api/docker-compose.yml ps
   docker-compose -f /root/workspace/shared/nginx/docker-compose.yml ps
   ```

## 安全建议

### 安全加固措施

1. **使用强密码**：
   - 数据库密码
   - 管理员账户密码

2. **定期更新镜像**：
   ```bash
   docker-compose pull
   docker-compose up -d
   ```

3. **配置防火墙规则**：
   ```bash
   # 只允许必要端口
   ufw allow 80/tcp
   ufw allow 443/tcp
   ufw deny 3000/tcp  # 不直接暴露应用端口
   ```

4. **启用访问日志监控**：
   - 监控异常访问模式
   - 设置告警机制

## 性能优化

### 优化建议

1. **Nginx 性能调优**：
   - 调整 worker_processes 和 worker_connections
   - 启用 gzip 压缩
   - 配置适当的缓存策略

2. **数据库优化**：
   - 定期清理日志表
   - 优化数据库索引
   - 配置适当的连接池大小

3. **容器资源限制**：
   ```yaml
   deploy:
     resources:
       limits:
         cpus: '1.0'
         memory: 1G
       reservations:
         cpus: '0.5'
         memory: 512M
   ```

## 总结

本部署指南提供了完整的 New-API 子路径部署方案，包括：

- 前端源码修改（API 基础 URL、Logo 路径等）
- Nginx 反向代理配置
- Docker 容器编排
- SSL 证书配置
- 安全加固措施
- 故障排除方法

按照本指南操作，可以成功将 New-API 部署在 `/ai/` 子路径下，实现稳定的 HTTPS 访问。

#!/bin/bash

# New-API 数据库管理脚本
# 功能：数据库备份、恢复、维护
# 版本：v1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 配置变量
NEW_API_DIR="/root/workspace/new-api"
BACKUP_DIR="/root/workspace/backup"
DB_NAME="new-api"
DB_USER="root"
DB_PASS="123456"
DB_CONTAINER="mysql"

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    if ! docker ps | grep -q "$DB_CONTAINER"; then
        log_error "MySQL 容器未运行"
        return 1
    fi
    
    if docker exec "$DB_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASS" -e "SELECT 1;" >/dev/null 2>&1; then
        log_success "数据库连接正常"
        return 0
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 备份数据库
backup_database() {
    local backup_name="${1:-$(date +%Y%m%d_%H%M%S)}"
    local backup_file="$BACKUP_DIR/database_${backup_name}.sql"
    
    log_info "开始备份数据库..."
    
    # 创建备份目录
    mkdir -p "$BACKUP_DIR"
    
    # 检查数据库连接
    if ! check_database; then
        log_error "数据库连接失败，无法备份"
        return 1
    fi
    
    # 执行备份
    log_info "备份到文件: $backup_file"
    if docker exec "$DB_CONTAINER" mysqldump -u"$DB_USER" -p"$DB_PASS" \
        --single-transaction --routines --triggers "$DB_NAME" > "$backup_file"; then
        
        local file_size=$(du -sh "$backup_file" | cut -f1)
        log_success "数据库备份完成"
        log_info "备份文件: $backup_file"
        log_info "文件大小: $file_size"
        
        # 验证备份文件
        if [ -s "$backup_file" ] && grep -q "CREATE TABLE" "$backup_file"; then
            log_success "备份文件验证通过"
        else
            log_warning "备份文件可能不完整，请检查"
        fi
        
        return 0
    else
        log_error "数据库备份失败"
        rm -f "$backup_file"
        return 1
    fi
}

# 恢复数据库
restore_database() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        log_error "请指定备份文件"
        echo "用法: $0 restore <备份文件路径>"
        echo "可用备份文件:"
        ls -la "$BACKUP_DIR"/database_*.sql 2>/dev/null || echo "  无可用备份文件"
        return 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        return 1
    fi
    
    log_warning "警告：此操作将覆盖当前数据库数据！"
    read -p "确认继续？(y/N): " confirm
    if [[ $confirm != [yY] ]]; then
        log_info "操作已取消"
        return 0
    fi
    
    log_info "开始恢复数据库..."
    log_info "备份文件: $backup_file"
    
    # 检查数据库连接
    if ! check_database; then
        log_error "数据库连接失败，无法恢复"
        return 1
    fi
    
    # 创建恢复前备份
    log_info "创建恢复前备份..."
    backup_database "before_restore_$(date +%Y%m%d_%H%M%S)"
    
    # 执行恢复
    log_info "执行数据库恢复..."
    if docker exec -i "$DB_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$backup_file"; then
        log_success "数据库恢复完成"
        
        # 验证恢复结果
        log_info "验证恢复结果..."
        local table_count=$(docker exec "$DB_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASS" -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$DB_NAME';" -s -N)
        log_info "数据库表数量: $table_count"
        
        if [ "$table_count" -gt 0 ]; then
            log_success "数据库恢复验证通过"
        else
            log_warning "数据库恢复后表数量为0，请检查"
        fi
        
        return 0
    else
        log_error "数据库恢复失败"
        return 1
    fi
}

# 数据库维护
maintain_database() {
    log_info "开始数据库维护..."
    
    if ! check_database; then
        log_error "数据库连接失败，无法执行维护"
        return 1
    fi
    
    # 优化表
    log_info "优化数据库表..."
    local tables=$(docker exec "$DB_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASS" -e "SHOW TABLES;" -s -N "$DB_NAME")
    
    if [ -n "$tables" ]; then
        echo "$tables" | while read table; do
            log_info "优化表: $table"
            docker exec "$DB_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASS" -e "OPTIMIZE TABLE $table;" "$DB_NAME" >/dev/null 2>&1 || log_warning "表 $table 优化失败"
        done
        log_success "数据库表优化完成"
    else
        log_warning "未找到数据库表"
    fi
    
    # 分析表
    log_info "分析数据库表..."
    if [ -n "$tables" ]; then
        echo "$tables" | while read table; do
            docker exec "$DB_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASS" -e "ANALYZE TABLE $table;" "$DB_NAME" >/dev/null 2>&1 || log_warning "表 $table 分析失败"
        done
        log_success "数据库表分析完成"
    fi
    
    # 显示数据库状态
    log_info "数据库状态信息:"
    echo "数据库大小:"
    docker exec "$DB_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASS" -e "
        SELECT 
            table_schema AS 'Database',
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
        FROM information_schema.tables 
        WHERE table_schema = '$DB_NAME'
        GROUP BY table_schema;
    " 2>/dev/null || log_warning "无法获取数据库大小信息"
    
    echo "表信息:"
    docker exec "$DB_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASS" -e "
        SELECT 
            table_name AS 'Table',
            table_rows AS 'Rows',
            ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
        FROM information_schema.tables 
        WHERE table_schema = '$DB_NAME'
        ORDER BY (data_length + index_length) DESC;
    " 2>/dev/null || log_warning "无法获取表信息"
}

# 清理旧备份
cleanup_backups() {
    local days="${1:-30}"
    
    log_info "清理 $days 天前的备份文件..."
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_info "备份目录不存在，无需清理"
        return 0
    fi
    
    local old_backups=$(find "$BACKUP_DIR" -name "database_*.sql" -mtime +$days 2>/dev/null)
    
    if [ -n "$old_backups" ]; then
        echo "将删除以下备份文件:"
        echo "$old_backups"
        
        read -p "确认删除？(y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            echo "$old_backups" | xargs rm -f
            log_success "旧备份文件清理完成"
        else
            log_info "清理操作已取消"
        fi
    else
        log_info "没有找到需要清理的旧备份文件"
    fi
}

# 显示备份列表
list_backups() {
    log_info "可用的数据库备份文件:"
    
    if [ -d "$BACKUP_DIR" ]; then
        local backups=$(ls -la "$BACKUP_DIR"/database_*.sql 2>/dev/null)
        if [ -n "$backups" ]; then
            echo "$backups"
        else
            log_info "没有找到备份文件"
        fi
    else
        log_info "备份目录不存在"
    fi
}

# 显示帮助信息
show_help() {
    echo "New-API 数据库管理脚本"
    echo
    echo "用法: $0 <命令> [参数]"
    echo
    echo "命令:"
    echo "  check                    - 检查数据库连接"
    echo "  backup [名称]           - 备份数据库（可选指定备份名称）"
    echo "  restore <备份文件>      - 恢复数据库"
    echo "  maintain                - 数据库维护（优化、分析）"
    echo "  cleanup [天数]          - 清理旧备份（默认30天）"
    echo "  list                    - 显示备份列表"
    echo "  help                    - 显示帮助信息"
    echo
    echo "示例:"
    echo "  $0 backup                           # 创建备份"
    echo "  $0 backup my_backup                 # 创建命名备份"
    echo "  $0 restore /path/to/backup.sql      # 恢复备份"
    echo "  $0 cleanup 7                        # 清理7天前的备份"
}

# 主函数
main() {
    case "${1:-help}" in
        "check")
            check_database
            ;;
        "backup")
            backup_database "$2"
            ;;
        "restore")
            restore_database "$2"
            ;;
        "maintain")
            maintain_database
            ;;
        "cleanup")
            cleanup_backups "$2"
            ;;
        "list")
            list_backups
            ;;
        "help"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

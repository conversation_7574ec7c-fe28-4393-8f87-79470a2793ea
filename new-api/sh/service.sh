#!/bin/bash

# New-API 服务管理脚本
# 功能：启动、停止、重启、状态查询
# 版本：v1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 配置变量
NEW_API_DIR="/root/workspace/new-api"
NGINX_DIR="/root/workspace/shared/nginx"

# 检查目录是否存在
check_directories() {
    if [ ! -d "$NEW_API_DIR" ]; then
        log_error "New-API 目录不存在: $NEW_API_DIR"
        return 1
    fi
    
    if [ ! -f "$NEW_API_DIR/docker-compose.yml" ]; then
        log_error "New-API docker-compose.yml 不存在"
        return 1
    fi
    
    return 0
}

# 启动服务
start_services() {
    log_info "启动 New-API 服务..."
    
    if ! check_directories; then
        return 1
    fi
    
    cd "$NEW_API_DIR"
    
    # 启动 New-API
    log_info "启动 New-API 容器..."
    if docker-compose up -d; then
        log_success "New-API 容器启动成功"
    else
        log_error "New-API 容器启动失败"
        return 1
    fi
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 15
    
    # 启动 Nginx（如果存在）
    if [ -d "$NGINX_DIR" ] && [ -f "$NGINX_DIR/docker-compose.yml" ]; then
        log_info "启动 Nginx 服务..."
        cd "$NGINX_DIR"
        if docker-compose up -d; then
            log_success "Nginx 服务启动成功"
        else
            log_warning "Nginx 服务启动失败"
        fi
        sleep 5
    else
        log_warning "Nginx 配置不存在，跳过启动"
    fi
    
    # 验证服务状态
    log_info "验证服务状态..."
    show_status
}

# 停止服务
stop_services() {
    log_info "停止 New-API 服务..."
    
    # 停止 Nginx
    if [ -d "$NGINX_DIR" ] && [ -f "$NGINX_DIR/docker-compose.yml" ]; then
        log_info "停止 Nginx 服务..."
        cd "$NGINX_DIR"
        docker-compose down || log_warning "Nginx 服务停止失败"
    fi
    
    # 停止 New-API
    if [ -d "$NEW_API_DIR" ] && [ -f "$NEW_API_DIR/docker-compose.yml" ]; then
        log_info "停止 New-API 容器..."
        cd "$NEW_API_DIR"
        if docker-compose down; then
            log_success "New-API 容器停止成功"
        else
            log_error "New-API 容器停止失败"
            return 1
        fi
    fi
    
    log_success "所有服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启 New-API 服务..."
    
    stop_services
    sleep 5
    start_services
    
    log_success "服务重启完成"
}

# 重新构建并启动
rebuild_services() {
    log_info "重新构建并启动服务..."
    
    if ! check_directories; then
        return 1
    fi
    
    # 停止服务
    stop_services
    
    # 清理 Docker 缓存
    log_info "清理 Docker 缓存..."
    docker system prune -f || log_warning "Docker 缓存清理失败"
    
    cd "$NEW_API_DIR"
    
    # 重新构建
    log_info "重新构建 New-API..."
    if docker-compose build --no-cache --pull; then
        log_success "New-API 构建成功"
    else
        log_error "New-API 构建失败"
        return 1
    fi
    
    # 启动服务
    start_services
    
    log_success "重新构建并启动完成"
}

# 显示服务状态
show_status() {
    log_info "服务状态检查..."
    
    echo "========================================"
    echo "容器状态"
    echo "========================================"
    
    # New-API 容器状态
    echo "New-API 容器:"
    if [ -d "$NEW_API_DIR" ] && [ -f "$NEW_API_DIR/docker-compose.yml" ]; then
        docker-compose -f "$NEW_API_DIR/docker-compose.yml" ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "  无法获取状态"
    else
        echo "  配置不存在"
    fi
    
    echo
    echo "Nginx 容器:"
    if [ -d "$NGINX_DIR" ] && [ -f "$NGINX_DIR/docker-compose.yml" ]; then
        docker-compose -f "$NGINX_DIR/docker-compose.yml" ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "  无法获取状态"
    else
        echo "  配置不存在"
    fi
    
    echo
    echo "========================================"
    echo "服务连通性测试"
    echo "========================================"
    
    # 测试主页
    local homepage_status=$(curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ai/ 2>/dev/null || echo "000")
    if [ "$homepage_status" = "200" ]; then
        log_success "✓ 主页访问正常 (状态码: $homepage_status)"
    else
        log_error "✗ 主页访问失败 (状态码: $homepage_status)"
    fi
    
    # 测试 API
    local api_response=$(curl -k -s https://localhost/ai/api/status 2>/dev/null || echo "")
    if echo "$api_response" | grep -q "success"; then
        log_success "✓ API 服务正常"
    else
        log_error "✗ API 服务异常"
    fi
    
    # 测试 Logo
    local logo_status=$(curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ai/logo.png 2>/dev/null || echo "000")
    if [ "$logo_status" = "200" ]; then
        log_success "✓ Logo 图片正常 (状态码: $logo_status)"
    else
        log_error "✗ Logo 图片失败 (状态码: $logo_status)"
    fi
    
    echo
    echo "========================================"
    echo "系统资源"
    echo "========================================"
    
    # 内存使用
    echo "内存使用:"
    free -h | grep -E "(Mem|Swap)"
    
    echo
    echo "磁盘使用:"
    df -h /root/workspace/ | head -1
    df -h /root/workspace/ | tail -1
    
    echo
    echo "端口监听:"
    netstat -tlnp | grep -E ":80|:443|:3000" || echo "未找到相关端口监听"
}

# 查看日志
show_logs() {
    local service="${1:-new-api}"
    local lines="${2:-50}"
    
    case "$service" in
        "new-api"|"api")
            log_info "显示 New-API 日志 (最近 $lines 行)..."
            if [ -d "$NEW_API_DIR" ] && [ -f "$NEW_API_DIR/docker-compose.yml" ]; then
                docker-compose -f "$NEW_API_DIR/docker-compose.yml" logs --tail="$lines" new-api
            else
                log_error "New-API 配置不存在"
            fi
            ;;
        "nginx")
            log_info "显示 Nginx 日志 (最近 $lines 行)..."
            if [ -d "$NGINX_DIR" ] && [ -f "$NGINX_DIR/docker-compose.yml" ]; then
                docker-compose -f "$NGINX_DIR/docker-compose.yml" logs --tail="$lines" nginx-proxy
            else
                log_error "Nginx 配置不存在"
            fi
            ;;
        "all")
            show_logs "new-api" "$lines"
            echo
            show_logs "nginx" "$lines"
            ;;
        *)
            log_error "未知服务: $service"
            echo "可用服务: new-api, nginx, all"
            return 1
            ;;
    esac
}

# 跟踪日志
follow_logs() {
    local service="${1:-new-api}"
    
    case "$service" in
        "new-api"|"api")
            log_info "跟踪 New-API 日志 (Ctrl+C 退出)..."
            if [ -d "$NEW_API_DIR" ] && [ -f "$NEW_API_DIR/docker-compose.yml" ]; then
                docker-compose -f "$NEW_API_DIR/docker-compose.yml" logs -f new-api
            else
                log_error "New-API 配置不存在"
            fi
            ;;
        "nginx")
            log_info "跟踪 Nginx 日志 (Ctrl+C 退出)..."
            if [ -d "$NGINX_DIR" ] && [ -f "$NGINX_DIR/docker-compose.yml" ]; then
                docker-compose -f "$NGINX_DIR/docker-compose.yml" logs -f nginx-proxy
            else
                log_error "Nginx 配置不存在"
            fi
            ;;
        *)
            log_error "未知服务: $service"
            echo "可用服务: new-api, nginx"
            return 1
            ;;
    esac
}

# 进入容器
enter_container() {
    local service="${1:-new-api}"
    
    case "$service" in
        "new-api"|"api")
            log_info "进入 New-API 容器..."
            if docker ps | grep -q new-api; then
                docker exec -it $(docker ps | grep new-api | awk '{print $1}') bash
            else
                log_error "New-API 容器未运行"
            fi
            ;;
        "nginx")
            log_info "进入 Nginx 容器..."
            if docker ps | grep -q nginx-proxy; then
                docker exec -it $(docker ps | grep nginx-proxy | awk '{print $1}') bash
            else
                log_error "Nginx 容器未运行"
            fi
            ;;
        "mysql")
            log_info "进入 MySQL 容器..."
            if docker ps | grep -q mysql; then
                docker exec -it mysql mysql -uroot -p123456
            else
                log_error "MySQL 容器未运行"
            fi
            ;;
        *)
            log_error "未知服务: $service"
            echo "可用服务: new-api, nginx, mysql"
            return 1
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "New-API 服务管理脚本"
    echo
    echo "用法: $0 <命令> [参数]"
    echo
    echo "命令:"
    echo "  start                   - 启动所有服务"
    echo "  stop                    - 停止所有服务"
    echo "  restart                 - 重启所有服务"
    echo "  rebuild                 - 重新构建并启动服务"
    echo "  status                  - 显示服务状态"
    echo "  logs <服务> [行数]      - 显示日志"
    echo "  follow <服务>           - 跟踪日志"
    echo "  enter <服务>            - 进入容器"
    echo "  help                    - 显示帮助信息"
    echo
    echo "服务名称:"
    echo "  new-api, api            - New-API 服务"
    echo "  nginx                   - Nginx 服务"
    echo "  mysql                   - MySQL 数据库"
    echo "  all                     - 所有服务（仅适用于 logs 命令）"
    echo
    echo "示例:"
    echo "  $0 start                        # 启动所有服务"
    echo "  $0 logs new-api 100             # 显示 New-API 最近100行日志"
    echo "  $0 follow nginx                 # 跟踪 Nginx 日志"
    echo "  $0 enter mysql                  # 进入 MySQL 容器"
}

# 主函数
main() {
    case "${1:-help}" in
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "rebuild")
            rebuild_services
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$2" "$3"
            ;;
        "follow")
            follow_logs "$2"
            ;;
        "enter")
            enter_container "$2"
            ;;
        "help"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

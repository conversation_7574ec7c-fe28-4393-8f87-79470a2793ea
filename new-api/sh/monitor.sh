#!/bin/bash

# New-API 服务监控脚本
# 功能：监控服务状态、性能指标、错误日志
# 版本：v1.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

NEW_API_DIR="/root/workspace/new-api"
NGINX_DIR="/root/workspace/shared/nginx"

# 检查服务函数
check_service() {
    local service_name="$1"
    local check_url="$2"
    local expected_status="${3:-200}"
    
    local status_code=$(curl -k -s -o /dev/null -w "%{http_code}" "$check_url" 2>/dev/null)
    local response_time=$(curl -k -s -o /dev/null -w "%{time_total}" "$check_url" 2>/dev/null)
    
    if [ "$status_code" = "$expected_status" ]; then
        log_success "✓ $service_name 正常 (状态码: $status_code, 响应时间: ${response_time}s)"
        return 0
    else
        log_error "✗ $service_name 异常 (状态码: $status_code, 响应时间: ${response_time}s)"
        return 1
    fi
}

# 检查API响应内容
check_api_content() {
    local api_name="$1"
    local api_url="$2"
    local expected_key="$3"
    
    local response=$(curl -k -s "$api_url" 2>/dev/null)
    
    if echo "$response" | grep -q "$expected_key"; then
        log_success "✓ $api_name API 内容正常"
        return 0
    else
        log_error "✗ $api_name API 内容异常"
        echo "   响应: $response"
        return 1
    fi
}

# 主监控函数
main_monitor() {
    echo "========================================"
    echo "    New-API 服务监控报告"
    echo "    时间: $(date)"
    echo "========================================"
    
    local total_checks=0
    local passed_checks=0
    
    # 1. 基础服务检查
    log_info "1. 基础服务检查"
    
    ((total_checks++))
    if check_service "主页" "https://localhost/ai/"; then
        ((passed_checks++))
    fi
    
    ((total_checks++))
    if check_service "Logo图片" "https://localhost/ai/logo.png"; then
        ((passed_checks++))
    fi
    
    # 2. API服务检查
    log_info "2. API服务检查"
    
    ((total_checks++))
    if check_api_content "状态API" "https://localhost/ai/api/status" "success"; then
        ((passed_checks++))
    fi
    
    # 检查其他API端点（允许404，因为可能未配置）
    for endpoint in "notice" "home_page_content"; do
        ((total_checks++))
        if check_service "API $endpoint" "https://localhost/ai/api/$endpoint" "200" || 
           check_service "API $endpoint" "https://localhost/ai/api/$endpoint" "404"; then
            ((passed_checks++))
        fi
    done
    
    # 3. 静态资源检查
    log_info "3. 静态资源检查"
    
    local html_content=$(curl -k -s https://localhost/ai/ 2>/dev/null)
    if [ -n "$html_content" ]; then
        # 检查JS文件
        local js_file=$(echo "$html_content" | grep -o '/ai/assets/index-[^"]*\.js' | head -1)
        if [ -n "$js_file" ]; then
            ((total_checks++))
            if check_service "主JS文件" "https://localhost$js_file"; then
                ((passed_checks++))
            fi
        fi
        
        # 检查CSS文件
        local css_file=$(echo "$html_content" | grep -o '/ai/assets/index-[^"]*\.css' | head -1)
        if [ -n "$css_file" ]; then
            ((total_checks++))
            if check_service "主CSS文件" "https://localhost$css_file"; then
                ((passed_checks++))
            fi
        fi
    else
        log_warning "无法获取HTML内容，跳过静态资源检查"
    fi
    
    # 4. 容器状态检查
    log_info "4. 容器状态检查"
    echo "New-API 容器:"
    if docker-compose -f "$NEW_API_DIR/docker-compose.yml" ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null; then
        log_success "✓ New-API 容器状态获取成功"
    else
        log_error "✗ 无法获取 New-API 容器状态"
    fi
    
    echo
    echo "Nginx 容器:"
    if [ -d "$NGINX_DIR" ] && docker-compose -f "$NGINX_DIR/docker-compose.yml" ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null; then
        log_success "✓ Nginx 容器状态获取成功"
    else
        log_warning "无法获取 Nginx 容器状态"
    fi
    
    # 5. 系统资源检查
    log_info "5. 系统资源检查"
    
    # 内存使用
    local mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    echo "内存使用率: ${mem_usage}%"
    if (( $(echo "$mem_usage > 80" | bc -l) )); then
        log_warning "内存使用率较高: ${mem_usage}%"
    else
        log_success "内存使用率正常: ${mem_usage}%"
    fi
    
    # 磁盘使用
    local disk_usage=$(df /root/workspace/ | tail -1 | awk '{print $5}' | sed 's/%//')
    echo "磁盘使用率: ${disk_usage}%"
    if [ "$disk_usage" -gt 80 ]; then
        log_warning "磁盘使用率较高: ${disk_usage}%"
    else
        log_success "磁盘使用率正常: ${disk_usage}%"
    fi
    
    # 6. 错误日志检查
    log_info "6. 错误日志检查（最近1小时）"
    
    echo "New-API 错误日志:"
    local new_api_errors=$(docker-compose -f "$NEW_API_DIR/docker-compose.yml" logs --since=1h new-api 2>/dev/null | grep -i error | wc -l)
    if [ "$new_api_errors" -gt 0 ]; then
        log_warning "发现 $new_api_errors 条错误日志"
        docker-compose -f "$NEW_API_DIR/docker-compose.yml" logs --since=1h new-api 2>/dev/null | grep -i error | tail -3
    else
        log_success "无错误日志"
    fi
    
    if [ -d "$NGINX_DIR" ]; then
        echo "Nginx 错误日志:"
        local nginx_errors=$(docker-compose -f "$NGINX_DIR/docker-compose.yml" logs --since=1h nginx-proxy 2>/dev/null | grep -E "(error|404|500)" | wc -l)
        if [ "$nginx_errors" -gt 0 ]; then
            log_warning "发现 $nginx_errors 条错误/异常日志"
            docker-compose -f "$NGINX_DIR/docker-compose.yml" logs --since=1h nginx-proxy 2>/dev/null | grep -E "(error|404|500)" | tail -3
        else
            log_success "无错误日志"
        fi
    fi
    
    # 7. 性能指标
    log_info "7. 性能指标"
    
    # 主页响应时间
    local homepage_time=$(curl -k -s -o /dev/null -w "%{time_total}" https://localhost/ai/ 2>/dev/null)
    echo "主页响应时间: ${homepage_time}s"
    if (( $(echo "$homepage_time > 3.0" | bc -l) )); then
        log_warning "主页响应时间较慢: ${homepage_time}s"
    else
        log_success "主页响应时间正常: ${homepage_time}s"
    fi
    
    # API响应时间
    local api_time=$(curl -k -s -o /dev/null -w "%{time_total}" https://localhost/ai/api/status 2>/dev/null)
    echo "API响应时间: ${api_time}s"
    if (( $(echo "$api_time > 2.0" | bc -l) )); then
        log_warning "API响应时间较慢: ${api_time}s"
    else
        log_success "API响应时间正常: ${api_time}s"
    fi
    
    # 8. 总结
    echo
    echo "========================================"
    echo "监控总结"
    echo "========================================"
    echo "检查项目: $total_checks"
    echo "通过项目: $passed_checks"
    echo "成功率: $(( passed_checks * 100 / total_checks ))%"
    
    if [ $passed_checks -eq $total_checks ]; then
        log_success "所有检查项目通过，服务运行正常！"
        return 0
    elif [ $passed_checks -gt $(( total_checks * 7 / 10 )) ]; then
        log_warning "大部分检查项目通过，但存在一些问题需要关注"
        return 1
    else
        log_error "多个检查项目失败，服务可能存在严重问题！"
        return 2
    fi
}

# 快速检查函数
quick_check() {
    echo "========================================"
    echo "    New-API 快速状态检查"
    echo "    时间: $(date)"
    echo "========================================"
    
    # 只检查关键服务
    check_service "主页" "https://localhost/ai/"
    check_api_content "状态API" "https://localhost/ai/api/status" "success"
    
    # 容器状态
    echo "容器状态:"
    docker-compose -f "$NEW_API_DIR/docker-compose.yml" ps --format "{{.Name}}: {{.Status}}" 2>/dev/null || echo "无法获取容器状态"
}

# 脚本入口
case "${1:-monitor}" in
    "monitor"|"")
        main_monitor
        ;;
    "quick")
        quick_check
        ;;
    "help")
        echo "用法: $0 [monitor|quick|help]"
        echo "  monitor (默认): 完整监控检查"
        echo "  quick: 快速状态检查"
        echo "  help: 显示帮助信息"
        ;;
    *)
        echo "未知参数: $1"
        echo "使用 '$0 help' 查看帮助"
        exit 1
        ;;
esac

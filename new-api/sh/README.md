# New-API 子脚本目录说明

本目录包含 New-API 项目的所有功能子脚本，每个脚本负责特定的管理功能。所有脚本都可以独立运行，也可以通过主管理脚本 `../manage-new-api.sh` 调用。

## 📁 脚本概览

| 脚本名称 | 主要功能 | 版本 | 状态 |
|---------|---------|------|------|
| `service.sh` | 服务管理 | v1.0 | ✅ 稳定 |
| `database.sh` | 数据库管理 | v1.0 | ✅ 稳定 |
| `update.sh` | 代码更新 | v1.0 | ✅ 稳定 |
| `monitor.sh` | 系统监控 | v1.0 | ✅ 稳定 |
| `quick-fix.sh` | 快速修复 | v1.0 | ✅ 稳定 |
| `auto-update.sh` | 自动更新 | v2.0 | ✅ 稳定 |
| `gemini-patch.sh` | Gemini修复 | v1.0 | ✅ 稳定 |

## 🔧 详细功能说明

### 1. service.sh - 服务管理脚本

**功能**：Docker 容器服务的完整生命周期管理

**主要命令**：
```bash
./service.sh start          # 启动 New-API 和 Nginx 服务
./service.sh stop           # 停止所有相关服务
./service.sh restart        # 重启所有服务
./service.sh rebuild        # 停止→清理缓存→重建→启动
./service.sh status         # 显示容器状态和连通性测试
./service.sh logs <service> # 查看指定服务日志
./service.sh follow <service> # 实时跟踪服务日志
./service.sh enter <service>  # 进入指定容器
```

**管理的服务**：
- New-API 主服务容器
- MySQL 数据库容器
- Nginx 反向代理容器

**特色功能**：
- 智能服务状态检测
- 容器健康检查
- 端口监听验证
- 日志实时跟踪

---

### 2. database.sh - 数据库管理脚本

**功能**：MySQL 数据库的备份、恢复和维护

**主要命令**：
```bash
./database.sh check                    # 检查数据库连接状态
./database.sh backup [备份名称]        # 创建数据库备份
./database.sh restore <备份文件路径>   # 从备份恢复数据库
./database.sh maintain                 # 数据库优化和维护
./database.sh list                     # 列出所有备份文件
./database.sh cleanup <天数>           # 清理指定天数前的备份
```

**备份特性**：
- 自动时间戳命名
- 完整数据库结构和数据备份
- 备份文件压缩存储
- 支持自定义备份名称

**存储位置**：
- 备份目录：`/root/workspace/new-api/backup/`
- 最新备份链接：`latest_backup.sql`

---

### 3. update.sh - 代码更新脚本

**功能**：Git 代码管理和补丁应用

**主要命令**：
```bash
./update.sh check           # 检查 Git 状态和未提交更改
./update.sh pull [--force]  # 拉取最新代码（可强制）
./update.sh patch           # 应用子路径修复补丁
./update.sh api-patch       # 应用 API 端点补丁
./update.sh verify          # 验证所有补丁是否正确应用
./update.sh update [--force] # 完整更新流程（拉取+补丁+验证）
```

**补丁管理**：
- 子路径修复：解决 `/ai` 路径下的静态资源问题
- API 端点修复：修复 API 路径配置
- 配置验证：确保关键配置正确应用

**安全特性**：
- 更新前自动检查未提交更改
- 支持强制更新模式
- 详细的验证和报告

---

### 4. monitor.sh - 系统监控脚本

**功能**：全面的服务状态和性能监控

**主要命令**：
```bash
./monitor.sh monitor        # 完整系统监控（8项检查）
./monitor.sh quick          # 快速状态检查（关键服务）
```

**监控项目**：
1. **基础服务检查**：主页访问、Logo 图片、静态资源
2. **API 服务检查**：状态 API、关键端点响应
3. **容器状态检查**：Docker 容器运行状态
4. **网络连接检查**：端口监听、服务连通性
5. **资源使用检查**：内存、磁盘、CPU 使用率
6. **日志错误检查**：最近错误日志分析
7. **性能指标检查**：响应时间、负载情况
8. **配置完整性检查**：关键配置文件验证

**报告功能**：
- 健康度评分（0-100%）
- 详细的检查结果
- 性能指标统计
- 问题诊断建议

---

### 5. quick-fix.sh - 快速修复脚本

**功能**：解决常见的静态资源和配置问题

**修复项目**：
1. **静态资源修复**：
   - API 基础 URL 配置
   - Logo 路径配置
   - HTML 模板配置
   - Vite 构建配置
   - React Router 配置

2. **Nginx 重定向修复**：
   - 解决 API 重定向循环
   - 修复代理配置

3. **CSP 策略修复**：
   - 内容安全策略配置
   - 静态资源访问权限

**执行流程**：
```
检查环境 → 修复静态资源 → 修复重定向 → 修复CSP → 重建测试 → 验证结果
```

**特色功能**：
- 自动备份原始配置
- 强制应用正确配置
- 重建和验证一体化
- 详细的修复报告

---

### 6. auto-update.sh - 自动更新脚本

**功能**：最完整的自动化更新流程

**更新流程**：
```
依赖检查 → 服务状态检查 → 数据备份 → 停止服务 → 拉取代码 → 
应用补丁 → 验证配置 → 重建服务 → 启动服务 → 验证更新 → 生成报告
```

**主要特性**：
- **智能备份**：自动备份到 `/root/workspace/backup/`
- **依赖检查**：验证 git、docker、curl、jq 等命令
- **配置验证**：检查所有关键配置项
- **服务测试**：验证主页、API、静态资源访问
- **回滚支持**：更新失败时支持快速回滚
- **详细日志**：记录所有操作到 `update.log`

**安全保障**：
- 更新前完整备份
- 分步骤验证
- 失败自动回滚
- 详细错误报告

---

### 7. gemini-patch.sh - Gemini API 修复脚本

**功能**：修复 Gemini API 模型列表端点错误

**问题描述**：
- 官方代码使用错误端点：`v1beta/openai/models`
- 正确端点应该是：`v1beta/models`

**主要命令**：
```bash
./gemini-patch.sh check     # 检查是否需要修复
./gemini-patch.sh apply     # 应用修复补丁
./gemini-patch.sh verify    # 验证修复结果
./gemini-patch.sh details   # 显示补丁详细信息
```

**修复流程**：
1. 检查目标文件存在性
2. 检查是否需要修复
3. 创建备份文件
4. 应用端点修复
5. 验证修复结果

**影响文件**：
- `controller/channel.go`（第174行左右）

## 🚀 使用建议

### 日常运维场景

1. **启动服务**：
   ```bash
   ./service.sh start
   ```

2. **检查系统状态**：
   ```bash
   ./monitor.sh quick
   ```

3. **定期备份**：
   ```bash
   ./database.sh backup daily_$(date +%Y%m%d)
   ```

4. **更新到最新版本**：
   ```bash
   ./auto-update.sh
   ```

### 故障排除场景

1. **静态资源404错误**：
   ```bash
   ./quick-fix.sh
   ```

2. **服务异常**：
   ```bash
   ./service.sh status
   ./service.sh logs new-api
   ./service.sh rebuild
   ```

3. **完整系统检查**：
   ```bash
   ./monitor.sh monitor
   ```

### 开发维护场景

1. **代码更新**：
   ```bash
   ./update.sh check
   ./update.sh update
   ```

2. **Gemini API 问题**：
   ```bash
   ./gemini-patch.sh check
   ./gemini-patch.sh apply
   ```

3. **数据库维护**：
   ```bash
   ./database.sh maintain
   ./database.sh cleanup 30
   ```

## 📝 注意事项

1. **权限要求**：所有脚本需要 root 权限或 sudo 权限
2. **依赖环境**：需要 Docker、Git、MySQL 客户端等工具
3. **备份策略**：重要操作前建议先备份数据库
4. **日志查看**：操作日志记录在各自的日志文件中
5. **错误处理**：脚本遇到错误会自动停止（set -e）

## 🔗 相关文档

- [主管理脚本说明](../README-脚本系统.md)
- [错误解决指南](../new-api错误指南.md)
- [更新操作指导](../new-api更新指导.md)
- [补丁系统说明](../patches/README.md)

---

**维护者**：AI Assistant  
**最后更新**：2025-07-27  
**版本**：v1.0

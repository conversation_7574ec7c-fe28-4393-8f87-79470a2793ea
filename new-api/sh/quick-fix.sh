#!/bin/bash

# New-API 快速修复脚本
# 专门解决静态资源404、重定向循环、CSP等常见错误
# 版本：v1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

NEW_API_DIR="/root/workspace/new-api"
NGINX_DIR="/root/workspace/shared/nginx"

echo "========================================"
echo "    New-API 快速修复脚本 v1.0"
echo "========================================"
echo "专门解决以下问题："
echo "1. 静态资源404错误"
echo "2. API重定向循环"
echo "3. CSP策略错误"
echo "4. MIME类型错误"
echo "========================================"

# 检查环境
check_environment() {
    log_info "检查环境..."
    
    if [ ! -d "$NEW_API_DIR" ]; then
        log_error "New-API 目录不存在: $NEW_API_DIR"
        exit 1
    fi
    
    cd "$NEW_API_DIR"
    log_success "环境检查通过"
}

# 修复静态资源路径问题
fix_static_resources() {
    log_info "修复静态资源路径问题..."
    
    cd "$NEW_API_DIR"
    
    # 1. 强制修复 API 基础 URL
    log_info "1. 修复 API 基础 URL..."
    if [ -f "web/src/helpers/api.js" ]; then
        # 备份原文件
        cp web/src/helpers/api.js web/src/helpers/api.js.backup
        
        # 创建新的 getApiBaseURL 函数
        cat > /tmp/api_fix.js << 'EOF'
// 动态获取API基础URL
function getApiBaseURL() {
  if (import.meta.env.VITE_REACT_APP_SERVER_URL) {
    return import.meta.env.VITE_REACT_APP_SERVER_URL;
  }

  // 强制使用/ai作为基础路径，因为应用部署在/ai子路径下
  return '/ai';
}
EOF
        
        # 替换函数
        sed -i '/function getApiBaseURL/,/^}/c\
// 动态获取API基础URL\
function getApiBaseURL() {\
  if (import.meta.env.VITE_REACT_APP_SERVER_URL) {\
    return import.meta.env.VITE_REACT_APP_SERVER_URL;\
  }\
\
  // 强制使用/ai作为基础路径，因为应用部署在/ai子路径下\
  return '\''/ai'\'';\
}' web/src/helpers/api.js
        
        log_success "API 基础 URL 修复完成"
    else
        log_warning "api.js 文件不存在"
    fi
    
    # 2. 修复 Logo 路径
    log_info "2. 修复 Logo 路径..."
    if [ -f "web/src/helpers/utils.js" ]; then
        cp web/src/helpers/utils.js web/src/helpers/utils.js.backup
        sed -i "s|return '/logo.png'|return '/ai/logo.png'|g" web/src/helpers/utils.js
        sed -i 's|return "/logo.png"|return "/ai/logo.png"|g' web/src/helpers/utils.js
        log_success "Logo 路径修复完成"
    else
        log_warning "utils.js 文件不存在"
    fi
    
    # 3. 修复 HTML 模板
    log_info "3. 修复 HTML 模板..."
    if [ -f "web/index.html" ]; then
        cp web/index.html web/index.html.backup
        sed -i 's|href="/logo.png"|href="/ai/logo.png"|g' web/index.html
        sed -i 's|src="/logo.png"|src="/ai/logo.png"|g' web/index.html
        log_success "HTML 模板修复完成"
    else
        log_warning "index.html 文件不存在"
    fi
    
    # 4. 强制修复 Vite 配置
    log_info "4. 修复 Vite 配置..."
    if [ -f "web/vite.config.js" ]; then
        cp web/vite.config.js web/vite.config.js.backup
        
        # 确保 base 配置正确
        if grep -q "base:" web/vite.config.js; then
            sed -i "s|base: ['\"][^'\"]*['\"]|base: '/ai/'|g" web/vite.config.js
        else
            # 如果没有 base 配置，添加它
            sed -i '/export default defineConfig/a\  base: '\''/ai/'\'',' web/vite.config.js
        fi
        log_success "Vite 配置修复完成"
    else
        log_warning "vite.config.js 文件不存在"
    fi
    
    # 5. 修复 React Router 配置
    log_info "5. 修复 React Router 配置..."
    if [ -f "web/src/index.js" ]; then
        cp web/src/index.js web/src/index.js.backup
        
        # 确保 basename 配置正确
        if grep -q "basename=" web/src/index.js; then
            sed -i 's|basename="[^"]*"|basename="/ai"|g' web/src/index.js
        else
            # 如果没有 basename，添加它
            sed -i 's|<BrowserRouter|<BrowserRouter basename="/ai"|g' web/src/index.js
        fi
        log_success "React Router 配置修复完成"
    else
        log_warning "index.js 文件不存在"
    fi
}

# 修复 Nginx 重定向循环
fix_nginx_redirects() {
    log_info "修复 Nginx 重定向循环..."
    
    # 创建修复后的 Nginx 配置
    cat > /tmp/nginx-fix.conf << 'EOF'
# 修复重定向循环的配置
location /ai/api/ {
    include /etc/nginx/includes/proxy-common.conf;
    rewrite ^/ai/(.*)$ /$1 break;
    proxy_pass http://new-api;
    proxy_set_header X-Forwarded-Prefix /ai;
    limit_req zone=api burst=10 nodelay;
}

location /ai/ {
    include /etc/nginx/includes/proxy-common.conf;
    rewrite ^/ai/(.*)$ /$1 break;
    proxy_pass http://new-api/;
    proxy_set_header X-Forwarded-Prefix /ai;
    limit_req zone=api burst=10 nodelay;
}
EOF
    
    log_info "Nginx 修复配置已保存到 /tmp/nginx-fix.conf"
    log_warning "请手动将此配置应用到 Nginx 配置文件中"
}

# 修复 CSP 策略
fix_csp_policy() {
    log_info "修复 CSP 策略..."
    
    # 创建 CSP 修复配置
    cat > /tmp/csp-fix.conf << 'EOF'
# 修复 CSP 配置以支持 blob URLs
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: blob:; font-src 'self' data:; connect-src 'self' https: wss:; frame-src 'self'; object-src 'none';" always;
EOF
    
    log_info "CSP 修复配置已保存到 /tmp/csp-fix.conf"
    log_warning "请将此配置添加到 Nginx 的 location 块中"
}

# 清除浏览器缓存提示
clear_browser_cache() {
    log_info "清除浏览器缓存提示..."
    
    echo
    echo "========================================"
    echo "重要：清除浏览器缓存"
    echo "========================================"
    echo "请执行以下操作之一："
    echo "1. 按 Ctrl+Shift+Delete 清除浏览器缓存"
    echo "2. 按 Ctrl+F5 强制刷新页面"
    echo "3. 在开发者工具中右键刷新按钮选择'清空缓存并硬性重新加载'"
    echo "========================================"
}

# 重新构建和测试
rebuild_and_test() {
    log_info "重新构建和测试..."
    
    cd "$NEW_API_DIR"
    
    # 停止服务
    log_info "停止服务..."
    docker-compose down
    
    # 清理 Docker 缓存
    log_info "清理 Docker 缓存..."
    docker system prune -f
    
    # 重新构建
    log_info "重新构建（无缓存）..."
    if docker-compose build --no-cache; then
        log_success "构建成功"
    else
        log_error "构建失败"
        exit 1
    fi
    
    # 启动服务
    log_info "启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 20
    
    # 启动 Nginx
    if [ -d "$NGINX_DIR" ]; then
        cd "$NGINX_DIR"
        docker-compose up -d
        sleep 10
    fi
}

# 验证修复结果
verify_fix() {
    log_info "验证修复结果..."
    
    echo "=== 配置验证 ==="
    
    # 检查关键配置
    cd "$NEW_API_DIR"
    
    if grep -q "return '/ai'" web/src/helpers/api.js 2>/dev/null; then
        log_success "✓ API 基础 URL 配置正确"
    else
        log_error "✗ API 基础 URL 配置错误"
    fi
    
    if grep -q "/ai/logo.png" web/src/helpers/utils.js 2>/dev/null; then
        log_success "✓ Logo 路径配置正确"
    else
        log_error "✗ Logo 路径配置错误"
    fi
    
    if grep -q "/ai/logo.png" web/index.html 2>/dev/null; then
        log_success "✓ HTML 模板配置正确"
    else
        log_error "✗ HTML 模板配置错误"
    fi
    
    if grep -q "base: '/ai/'" web/vite.config.js 2>/dev/null; then
        log_success "✓ Vite 配置正确"
    else
        log_error "✗ Vite 配置错误"
    fi
    
    echo
    echo "=== 服务测试 ==="
    
    # 测试服务
    sleep 5
    
    log_info "测试主页访问..."
    if curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ai/ | grep -q "200"; then
        log_success "✓ 主页访问正常"
    else
        log_warning "✗ 主页访问失败（可能需要清除浏览器缓存）"
    fi
    
    log_info "测试 API 状态..."
    if curl -k -s https://localhost/ai/api/status 2>/dev/null | grep -q "success"; then
        log_success "✓ API 状态正常"
    else
        log_warning "✗ API 状态异常"
    fi
    
    log_info "测试 Logo 图片..."
    if curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ai/logo.png | grep -q "200"; then
        log_success "✓ Logo 图片正常"
    else
        log_warning "✗ Logo 图片失败"
    fi
    
    echo
    echo "=== HTML 资源路径检查 ==="
    log_info "检查 HTML 中的资源路径..."
    if curl -k -s https://localhost/ai/ | grep -E "(assets|href=|src=)" | head -5; then
        log_info "HTML 资源路径如上所示"
    else
        log_warning "无法获取 HTML 内容"
    fi
}

# 主函数
main() {
    check_environment
    fix_static_resources
    fix_nginx_redirects
    fix_csp_policy
    rebuild_and_test
    verify_fix
    clear_browser_cache
    
    echo
    echo "========================================"
    echo "快速修复完成！"
    echo "========================================"
    echo "如果问题仍然存在，请："
    echo "1. 清除浏览器缓存"
    echo "2. 检查 Nginx 配置"
    echo "3. 查看容器日志: docker-compose logs"
    echo "4. 运行完整更新: ./auto-update.sh"
    echo "========================================"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

# New-API 脚本管理系统

本系统提供了完整的 New-API 管理脚本，包括服务管理、数据库操作、代码更新、系统监控等功能。

## 📁 目录结构

```
new-api/
├── manage-new-api.sh           # 综合管理脚本（主入口）
├── sh/                         # 子脚本目录
│   ├── service.sh             # 服务管理脚本
│   ├── database.sh            # 数据库管理脚本
│   ├── update.sh              # 代码更新脚本
│   ├── monitor.sh             # 系统监控脚本
│   ├── quick-fix.sh           # 快速修复脚本
│   └── auto-update.sh         # 自动更新脚本
├── new-api错误指南.md          # 错误解决指南
├── new-api更新指导.md          # 更新操作指导
└── README-脚本系统.md          # 本文档
```

## 🚀 快速开始

### 方式1：交互式菜单（推荐）
```bash
cd /root/workspace/new-api
./manage-new-api.sh
```

### 方式2：直接命令
```bash
# 启动服务
./manage-new-api.sh start

# 停止服务
./manage-new-api.sh stop

# 重启服务
./manage-new-api.sh restart

# 状态检查
./manage-new-api.sh status

# 数据库备份
./manage-new-api.sh backup

# 代码更新
./manage-new-api.sh update

# 快速修复
./manage-new-api.sh fix

# 系统监控
./manage-new-api.sh monitor
```

## 📋 功能模块

### 1. 服务管理 (service.sh)
- **启动服务**: 启动 New-API 和 Nginx 服务
- **停止服务**: 安全停止所有服务
- **重启服务**: 重启所有服务
- **重新构建**: 清理缓存并重新构建
- **状态查询**: 显示容器状态和连通性测试
- **日志查看**: 查看和跟踪服务日志
- **容器操作**: 进入容器进行调试

```bash
# 使用示例
./sh/service.sh start          # 启动服务
./sh/service.sh status         # 查看状态
./sh/service.sh logs new-api   # 查看日志
./sh/service.sh enter mysql    # 进入MySQL容器
```

### 2. 数据库管理 (database.sh)
- **连接检查**: 验证数据库连接状态
- **数据备份**: 创建完整的数据库备份
- **数据恢复**: 从备份文件恢复数据库
- **数据维护**: 优化和分析数据库表
- **备份管理**: 列出和清理旧备份文件

```bash
# 使用示例
./sh/database.sh check                    # 检查连接
./sh/database.sh backup my_backup         # 创建命名备份
./sh/database.sh restore /path/to/backup  # 恢复备份
./sh/database.sh maintain                 # 数据库维护
./sh/database.sh cleanup 7                # 清理7天前的备份
```

### 3. 代码更新 (update.sh)
- **Git状态检查**: 检查仓库状态和未提交更改
- **代码拉取**: 从远程仓库拉取最新代码
- **补丁应用**: 自动应用子路径配置补丁
- **配置验证**: 验证关键配置是否正确
- **完整更新**: 执行完整的更新流程

```bash
# 使用示例
./sh/update.sh check           # 检查Git状态
./sh/update.sh pull            # 拉取最新代码
./sh/update.sh patch           # 应用补丁
./sh/update.sh update          # 完整更新流程
```

### 4. 系统监控 (monitor.sh)
- **服务监控**: 检查所有服务的运行状态
- **性能监控**: 监控响应时间和系统资源
- **错误检查**: 分析错误日志和异常情况
- **连通性测试**: 测试API和静态资源访问
- **资源统计**: 显示内存、磁盘使用情况

```bash
# 使用示例
./sh/monitor.sh monitor        # 完整监控
./sh/monitor.sh quick          # 快速检查
```

### 5. 快速修复 (quick-fix.sh)
- **静态资源修复**: 解决404错误
- **重定向修复**: 解决API重定向循环
- **CSP策略修复**: 解决内容安全策略错误
- **配置强制修复**: 强制应用正确的子路径配置

```bash
# 使用示例
./sh/quick-fix.sh              # 执行快速修复
```

### 6. 自动更新 (auto-update.sh)
- **完整自动化**: 从备份到验证的完整流程
- **智能检测**: 自动检测和解决常见问题
- **详细日志**: 记录所有操作步骤和结果
- **回滚支持**: 更新失败时支持快速回滚

```bash
# 使用示例
./sh/auto-update.sh            # 执行自动更新
```

## 🔧 常用操作场景

### 场景1：日常启动服务
```bash
./manage-new-api.sh start
```

### 场景2：遇到静态资源404错误
```bash
./manage-new-api.sh fix
```

### 场景3：更新到最新版本
```bash
./manage-new-api.sh update
```

### 场景4：定期数据库备份
```bash
./sh/database.sh backup weekly_$(date +%Y%m%d)
```

### 场景5：服务异常排查
```bash
# 1. 检查服务状态
./manage-new-api.sh status

# 2. 查看错误日志
./sh/service.sh logs new-api

# 3. 系统监控
./manage-new-api.sh monitor

# 4. 如果需要，重新构建
./sh/service.sh rebuild
```

### 场景6：完整的更新和部署
```bash
# 1. 备份数据库
./sh/database.sh backup before_update_$(date +%Y%m%d)

# 2. 执行自动更新
./sh/auto-update.sh

# 3. 验证服务状态
./manage-new-api.sh status
```

## 📊 监控和维护

### 定期检查脚本
```bash
# 创建定期检查脚本
cat > /root/workspace/new-api/daily-check.sh << 'EOF'
#!/bin/bash
echo "=== $(date) 每日检查 ==="
cd /root/workspace/new-api

# 快速状态检查
./sh/monitor.sh quick

# 检查磁盘空间
df -h /root/workspace/

# 清理Docker缓存（如果磁盘空间不足）
DISK_USAGE=$(df /root/workspace/ | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 80 ]; then
    echo "磁盘空间不足，清理Docker缓存..."
    docker system prune -f
fi

echo "=== 检查完成 ==="
EOF

chmod +x /root/workspace/new-api/daily-check.sh

# 设置定时任务（可选）
# echo "0 9 * * * /root/workspace/new-api/daily-check.sh >> /var/log/new-api-daily.log 2>&1" | crontab -
```

### 备份策略
```bash
# 每日备份
./sh/database.sh backup daily_$(date +%Y%m%d)

# 每周备份
./sh/database.sh backup weekly_$(date +%Y%W)

# 清理30天前的备份
./sh/database.sh cleanup 30
```

## 🚨 故障排除

### 常见问题

1. **脚本权限错误**
   ```bash
   chmod +x /root/workspace/new-api/manage-new-api.sh
   chmod +x /root/workspace/new-api/sh/*.sh
   ```

2. **Docker服务未启动**
   ```bash
   systemctl start docker
   systemctl enable docker
   ```

3. **端口被占用**
   ```bash
   netstat -tlnp | grep :80
   netstat -tlnp | grep :443
   ```

4. **数据库连接失败**
   ```bash
   ./sh/service.sh logs mysql
   ./sh/database.sh check
   ```

5. **静态资源404错误**
   ```bash
   ./sh/quick-fix.sh
   ```

### 紧急恢复流程

1. **服务完全停止**
   ```bash
   ./sh/service.sh stop
   docker system prune -f
   ./sh/service.sh start
   ```

2. **数据库损坏**
   ```bash
   # 查看可用备份
   ./sh/database.sh list
   
   # 恢复最新备份
   ./sh/database.sh restore /path/to/latest/backup.sql
   ```

3. **代码配置错误**
   ```bash
   # 快速修复
   ./sh/quick-fix.sh
   
   # 或重新应用补丁
   ./sh/update.sh patch
   ```

## 📝 日志和调试

### 日志位置
- **更新日志**: `/root/workspace/new-api/update.log`
- **容器日志**: `docker-compose logs <service>`
- **系统日志**: `/var/log/`

### 调试模式
```bash
# 启用详细输出
set -x

# 执行脚本
./manage-new-api.sh status

# 关闭详细输出
set +x
```

## 🔄 版本更新

当脚本系统本身需要更新时：

```bash
# 备份当前脚本
cp -r sh sh.backup.$(date +%Y%m%d)

# 更新脚本（根据实际情况）
# git pull origin main

# 恢复执行权限
chmod +x manage-new-api.sh sh/*.sh
```

## 📞 支持和反馈

如果在使用过程中遇到问题：

1. 查看相关日志文件
2. 运行 `./manage-new-api.sh status` 检查系统状态
3. 参考 `new-api错误指南.md` 文档
4. 使用 `./sh/monitor.sh monitor` 进行全面检查

---

**注意**: 所有脚本都经过测试，但在生产环境中使用前，建议先在测试环境中验证。定期备份数据是最佳实践。

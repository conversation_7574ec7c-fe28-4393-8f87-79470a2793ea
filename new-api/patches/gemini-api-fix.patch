#!/bin/bash

# New-API Gemini API 端点修复补丁
# 功能：修复 Gemini API 模型列表端点错误
# 版本：v1.0
# 问题：官方代码使用了错误的端点 v1beta/openai/models，应该是 v1beta/models

# 补丁信息
PATCH_NAME="Gemini API 端点修复补丁"
PATCH_VERSION="v1.0"
PATCH_DESCRIPTION="修复 Gemini API 模型列表端点从 v1beta/openai/models 到 v1beta/models"

# 应用补丁
apply_gemini_patch() {
    local base_dir="$1"
    local patches_applied=0
    
    echo "[PATCH] 应用 $PATCH_NAME ($PATCH_VERSION)"
    echo "[PATCH] 描述: $PATCH_DESCRIPTION"
    echo "[PATCH] 目标目录: $base_dir"
    
    cd "$base_dir" || return 1
    
    # 检查目标文件是否存在
    if [ ! -f "controller/channel.go" ]; then
        echo "[PATCH] ! 目标文件不存在: controller/channel.go"
        return 1
    fi
    
    # 检查是否需要修复
    local need_fix=0

    # 检查 FetchUpstreamModels 函数中的端点
    if grep -q 'v1beta/openai/models' controller/channel.go; then
        echo "[PATCH] 发现错误的 Gemini API 端点: v1beta/openai/models"
        need_fix=1
    fi

    # 检查 FetchModels 函数是否需要添加 Gemini 支持
    if ! grep -A10 'url := fmt.Sprintf("%s/v1/models", baseURL)' controller/channel.go | grep -q 'case constant.ChannelTypeGemini:'; then
        echo "[PATCH] 发现 FetchModels 函数缺少 Gemini 端点支持"
        need_fix=1
    fi

    if [ $need_fix -eq 1 ]; then
        echo "[PATCH] 需要修复为正确的端点: v1beta/models"

        # 备份原文件
        cp controller/channel.go controller/channel.go.backup
        echo "[PATCH] 已创建备份: controller/channel.go.backup"

        # 应用修复1：将错误的端点替换为正确的端点
        if sed -i 's|v1beta/openai/models|v1beta/models|g' controller/channel.go; then
            echo "[PATCH] 修复1: 替换错误端点成功"
        else
            echo "[PATCH] ✗ 修复1失败"
            return 1
        fi

        # 应用修复2：为 FetchModels 函数添加 Gemini 支持
        if ! grep -A10 'url := fmt.Sprintf("%s/v1/models", baseURL)' controller/channel.go | grep -q 'case constant.ChannelTypeGemini:'; then
            # 在 FetchModels 函数中添加 Gemini 端点支持
            sed -i '/url := fmt.Sprintf("%s\/v1\/models", baseURL)/a\\tswitch req.Type {\n\tcase constant.ChannelTypeGemini:\n\t\turl = fmt.Sprintf("%s/v1beta/models", baseURL)\n\tcase constant.ChannelTypeAli:\n\t\turl = fmt.Sprintf("%s/compatible-mode/v1/models", baseURL)\n\t}' controller/channel.go
            echo "[PATCH] 修复2: 为 FetchModels 函数添加 Gemini 支持成功"
        fi

        # 应用修复3：为 FetchModels 函数添加 Gemini 模型 ID 处理
        if ! grep -A5 'for _, model := range result.Data {' controller/channel.go | grep -q 'if req.Type == constant.ChannelTypeGemini {'; then
            # 添加 Gemini 模型 ID 处理
            sed -i '/for _, model := range result.Data {/,/models = append(models, model.ID)/ {
                s/models = append(models, model.ID)/id := model.ID\n\t\tif req.Type == constant.ChannelTypeGemini {\n\t\t\tid = strings.TrimPrefix(id, "models\/")\n\t\t}\n\t\tmodels = append(models, id)/
            }' controller/channel.go
            echo "[PATCH] 修复3: 为 FetchModels 函数添加 Gemini 模型 ID 处理成功"
        fi

        ((patches_applied++))
    else
        echo "[PATCH] ✓ Gemini API 端点已正确，无需修复"
        ((patches_applied++))
    fi
    
    echo "[PATCH] 补丁应用完成，成功应用 $patches_applied 个修复"
    return 0
}

# 验证补丁
verify_gemini_patch() {
    local base_dir="$1"
    
    echo "[PATCH] 验证补丁应用结果..."
    
    cd "$base_dir" || return 1
    
    # 检查目标文件是否存在
    if [ ! -f "controller/channel.go" ]; then
        echo "[PATCH] ✗ 目标文件不存在: controller/channel.go"
        return 1
    fi
    
    # 检查是否还存在错误的端点
    if grep -q 'v1beta/openai/models' controller/channel.go; then
        echo "[PATCH] ✗ 验证失败：仍然存在错误的端点"
        echo "[PATCH] 错误端点位置:"
        grep -n 'v1beta/openai/models' controller/channel.go
        return 1
    fi
    
    # 检查是否存在正确的端点
    local correct_endpoints=0

    if grep -q 'v1beta/models' controller/channel.go; then
        ((correct_endpoints++))
    fi

    # 检查 FetchModels 函数是否有 Gemini 支持
    if grep -A10 'url := fmt.Sprintf("%s/v1/models", baseURL)' controller/channel.go | grep -q 'case constant.ChannelTypeGemini:'; then
        ((correct_endpoints++))
    fi

    # 检查 FetchModels 函数是否有 Gemini 模型 ID 处理
    if grep -A5 'for _, model := range result.Data {' controller/channel.go | grep -q 'if req.Type == constant.ChannelTypeGemini {'; then
        ((correct_endpoints++))
    fi

    if [ $correct_endpoints -ge 2 ]; then
        echo "[PATCH] ✓ 验证成功：端点已修复为正确格式"

        # 显示修复后的相关代码
        echo "[PATCH] 修复后的相关代码:"
        grep -n "v1beta/models" controller/channel.go | head -5
        echo "[PATCH] FetchModels 函数 Gemini 支持:"
        grep -A5 -B2 'case constant.ChannelTypeGemini:' controller/channel.go | head -10

        return 0
    else
        echo "[PATCH] ✗ 验证失败：修复不完整"
        echo "[PATCH] 正确端点数量: $correct_endpoints (需要至少2个)"
        return 1
    fi
}

# 显示修复详情
show_patch_details() {
    echo
    echo "[PATCH] === Gemini API 端点修复详情 ==="
    echo
    echo "问题描述："
    echo "  官方代码中使用了错误的 Gemini API 端点"
    echo "  错误端点: https://generativelanguage.googleapis.com/v1beta/openai/models"
    echo "  正确端点: https://generativelanguage.googleapis.com/v1beta/models"
    echo
    echo "影响功能："
    echo "  - 渠道管理页面的「获取模型」功能"
    echo "  - Gemini 渠道的模型列表获取"
    echo
    echo "修复方法："
    echo "  将 controller/channel.go 中的 'v1beta/openai/models' 替换为 'v1beta/models'"
    echo
    echo "参考文档："
    echo "  Google Gemini API 官方文档: https://ai.google.dev/api/models"
    echo
}

# 检查是否需要修复
check_need_patch() {
    local base_dir="$1"
    
    echo "[PATCH] 检查是否需要应用补丁..."
    
    cd "$base_dir" || return 1
    
    if [ ! -f "controller/channel.go" ]; then
        echo "[PATCH] ! 目标文件不存在: controller/channel.go"
        return 1
    fi
    
    local need_fix=0

    if grep -q 'v1beta/openai/models' controller/channel.go; then
        echo "[PATCH] 需要修复：发现错误的 Gemini API 端点"
        need_fix=1
    fi

    # 检查 FetchModels 函数是否需要添加 Gemini 支持
    if ! grep -A10 'url := fmt.Sprintf("%s/v1/models", baseURL)' controller/channel.go | grep -q 'case constant.ChannelTypeGemini:'; then
        echo "[PATCH] 需要修复：FetchModels 函数缺少 Gemini 端点支持"
        need_fix=1
    fi

    if [ $need_fix -eq 1 ]; then
        return 0
    else
        echo "[PATCH] 无需修复：Gemini API 端点已正确"
        return 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    case "${1:-help}" in
        "apply")
            apply_gemini_patch "${2:-/root/workspace/new-api}"
            ;;
        "verify")
            verify_gemini_patch "${2:-/root/workspace/new-api}"
            ;;
        "check")
            check_need_patch "${2:-/root/workspace/new-api}"
            ;;
        "details")
            show_patch_details
            ;;
        "help"|"")
            echo "用法: $0 <命令> [目录]"
            echo "命令:"
            echo "  apply [目录]   - 应用 Gemini API 端点修复补丁"
            echo "  verify [目录]  - 验证补丁应用结果"
            echo "  check [目录]   - 检查是否需要修复"
            echo "  details        - 显示修复详情"
            echo "  help          - 显示帮助信息"
            ;;
        *)
            echo "未知命令: $1"
            exit 1
            ;;
    esac
fi

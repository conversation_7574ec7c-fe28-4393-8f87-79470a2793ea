# New-API 总指导文档

## 概述

本文档是 New-API 项目的总体指导文档，提供了完整的部署、更新、错误处理和维护指南。New-API 是一个基于 Go 语言开发的 AI API 管理平台，本项目采用子路径部署方案，通过 nginx 反向代理实现 HTTPS 访问。

## 项目架构

### 系统架构图

```
┌─────────────────┐    HTTPS     ┌─────────────────┐    HTTP     ┌─────────────────┐
│                 │   (443/80)   │                 │   (3000)    │                 │
│   用户浏览器    │ ──────────► │   Nginx 代理    │ ──────────► │   New-API 服务  │
│                 │              │                 │             │                 │
└─────────────────┘              └─────────────────┘             └─────────────────┘
                                          │                               │
                                          │                               │
                                          ▼                               ▼
                                 ┌─────────────────┐             ┌─────────────────┐
                                 │   SSL 证书      │             │   MySQL 数据库  │
                                 │   静态文件      │             │   Redis 缓存    │
                                 └─────────────────┘             └─────────────────┘
```

### 核心组件

1. **New-API 服务**：主要的 API 管理服务，运行在端口 3000
2. **Nginx 代理**：反向代理服务器，处理 HTTPS 和路径重写
3. **MySQL 数据库**：存储用户数据、API 密钥等信息
4. **Redis 缓存**：提供缓存和会话存储
5. **前端应用**：基于 React 的管理界面

### 目录结构

```
/root/workspace/
├── new-api/                           # New-API 项目根目录
│   ├── web/                           # 前端源码目录
│   │   ├── src/
│   │   │   ├── helpers/
│   │   │   │   ├── api.js            # API 基础 URL 配置 ⭐
│   │   │   │   └── utils.js          # Logo 路径配置 ⭐
│   │   │   └── index.js              # React Router 配置 ⭐
│   │   ├── index.html                # HTML 模板 ⭐
│   │   └── vite.config.js            # Vite 构建配置 ⭐
│   ├── docker-compose.yml            # 服务编排配置
│   ├── Dockerfile                    # 构建配置
│   ├── data/                         # 数据持久化目录
│   ├── logs/                         # 日志目录
│   ├── patches/                      # 自定义补丁文件
│   ├── update-new-api.sh             # 自动更新脚本
│   ├── rollback-new-api.sh           # 回滚脚本
│   ├── new-api部署指南-详细版.md      # 详细部署指南
│   ├── new-api更新指导.md             # 更新指导
│   ├── new-api错误指南.md             # 错误处理指南
│   └── new-api总指导.md               # 本文档
└── shared/
    └── nginx/                         # Nginx 配置目录
        ├── docker-compose.yml         # Nginx 服务配置
        ├── nginx.conf                 # 主配置文件
        ├── conf.d/                    # 站点配置目录
        │   └── liangliangdamowang.edu.deal.conf  # 站点配置 ⭐
        ├── includes/                  # 包含文件目录
        │   ├── ssl-common.conf        # SSL 通用配置
        │   ├── security-headers.conf  # 安全头配置 ⭐
        │   ├── proxy-common.conf      # 代理通用配置
        │   └── access-control.conf    # 访问控制配置
        ├── ssl/                       # SSL 证书目录
        │   ├── certificate.crt        # SSL 证书文件
        │   └── private.key            # SSL 私钥文件
        └── logs/                      # 日志目录
            ├── access/                # 访问日志
            └── error/                 # 错误日志
```

**注：标记 ⭐ 的文件包含关键的子路径配置，更新时需要特别注意。**

## 文档导航

### 1. [部署指南](./new-api部署指南-详细版.md)

**适用场景**：首次部署或重新部署

**主要内容**：
- 完整的部署步骤
- 环境准备和依赖安装
- SSL 证书配置
- Nginx 反向代理设置
- 前端源码修改要点
- Docker 容器编排
- 部署验证和测试

**关键特性**：
- 基于源码修改的自定义构建
- 子路径 `/ai/` 部署方案
- HTTPS 安全访问
- 完整的配置文件示例

### 2. [更新指导](./new-api更新指导.md)

**适用场景**：官方版本更新或功能升级

**主要内容**：
- 安全的更新流程
- 备份和恢复策略
- 自定义修改的保护
- 自动化更新脚本
- 回滚方案

**关键特性**：
- 保护现有的 `/ai/` 路径配置
- 自动应用补丁文件
- 完整的备份机制
- 一键更新和回滚

### 3. [错误指南](./new-api错误指南.md)

**适用场景**：故障排除和问题解决

**主要内容**：
- 常见错误及解决方案
- 故障排除工具和命令
- 日志分析方法
- 紧急恢复步骤

**关键特性**：
- 涵盖所有已知问题
- 详细的解决步骤
- 预防措施建议
- 快速诊断方法

## 核心配置说明

### 子路径配置要点

本项目的核心特性是在子路径 `/ai/` 下部署，这需要在多个层面进行配置：

#### 1. 前端应用配置

**Vite 构建配置** (`web/vite.config.js`)：
```javascript
export default defineConfig({
  base: '/ai/',  // 设置基础路径
  // ...
});
```

**React Router 配置** (`web/src/index.js`)：
```javascript
<BrowserRouter basename="/ai">
  {/* 路由配置 */}
</BrowserRouter>
```

**API 基础 URL** (`web/src/helpers/api.js`)：
```javascript
function getApiBaseURL() {
  // 强制使用/ai作为基础路径
  return '/ai';
}
```

**静态资源路径** (`web/src/helpers/utils.js`, `web/index.html`)：
```javascript
// Logo 路径
if (!logo) return '/ai/logo.png';
```

```html
<!-- Favicon 路径 -->
<link rel="icon" href="/ai/logo.png" />
```

#### 2. Nginx 代理配置

**关键 location 配置顺序**：
```nginx
# 1. 特殊处理登录接口 - 最高优先级
location /ai/api/user/login { ... }

# 2. 特殊处理静态资源
location ~* /ai/.*\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ { ... }

# 3. 通用 API 处理
location /ai/ { ... }
```

**路径重写规则**：
```nginx
rewrite ^/ai/(.*)$ /$1 break;
proxy_pass http://new-api/;
proxy_set_header X-Forwarded-Prefix /ai;
```

### 安全配置

#### SSL/TLS 配置
- 支持 TLS 1.2 和 1.3
- 强制 HTTPS 重定向
- HSTS 安全头

#### 内容安全策略 (CSP)
```nginx
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: blob:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'self';" always;
```

#### 访问控制
- API 限流配置
- 连接数限制
- 登录接口特殊保护

## 常用操作命令

### 服务管理

```bash
# 启动所有服务
cd /root/workspace/new-api && docker-compose up -d
cd /root/workspace/shared/nginx && docker-compose up -d

# 停止所有服务
cd /root/workspace/new-api && docker-compose down
cd /root/workspace/shared/nginx && docker-compose down

# 重启服务
cd /root/workspace/new-api && docker-compose restart
cd /root/workspace/shared/nginx && docker-compose restart

# 查看服务状态
docker-compose ps
docker-compose logs -f new-api
```

### 配置管理

```bash
# 验证 nginx 配置
cd /root/workspace/shared/nginx
docker-compose exec nginx nginx -t

# 重新加载 nginx 配置
docker-compose exec nginx nginx -s reload

# 重新构建应用
cd /root/workspace/new-api
docker-compose build --no-cache
docker-compose up -d
```

### 监控和诊断

```bash
# 检查服务健康状态
curl -k -s https://localhost/ai/api/status | jq '.success'

# 测试静态资源
curl -k -I https://localhost/ai/logo.png

# 查看日志
tail -f /root/workspace/shared/nginx/logs/error/error.log
tail -f /root/workspace/new-api/logs/oneapi-*.log

# 检查容器资源使用
docker stats
```

### 备份和恢复

```bash
# 创建备份
BACKUP_DIR="/root/workspace/backup/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR
docker exec mysql mysqldump -uroot -p123456 new-api > $BACKUP_DIR/database.sql
cp -r /root/workspace/shared/nginx/conf.d/ $BACKUP_DIR/nginx_conf/
cp -r /root/workspace/new-api/data/ $BACKUP_DIR/new-api_data/

# 恢复备份
docker exec -i mysql mysql -uroot -p123456 new-api < $BACKUP_DIR/database.sql
cp -r $BACKUP_DIR/nginx_conf/* /root/workspace/shared/nginx/conf.d/
cp -r $BACKUP_DIR/new-api_data/* /root/workspace/new-api/data/
```

## 维护计划

### 日常维护

**每日检查**：
- [ ] 服务运行状态
- [ ] 日志文件大小
- [ ] 磁盘空间使用
- [ ] 内存和 CPU 使用率

**每周维护**：
- [ ] 清理旧日志文件
- [ ] 检查 SSL 证书有效期
- [ ] 更新系统安全补丁
- [ ] 备份重要数据

**每月维护**：
- [ ] 检查官方版本更新
- [ ] 性能优化评估
- [ ] 安全配置审查
- [ ] 备份策略验证

### 监控指标

**服务可用性**：
- HTTP 响应状态码
- 响应时间
- 服务启动状态

**资源使用**：
- CPU 使用率
- 内存使用率
- 磁盘空间
- 网络流量

**安全指标**：
- 异常访问模式
- 错误日志数量
- SSL 证书状态
- 安全头配置

## 故障处理流程

### 1. 问题识别
- 收集错误信息和日志
- 确定影响范围
- 评估紧急程度

### 2. 初步诊断
- 检查服务状态
- 查看相关日志
- 测试网络连接

### 3. 问题解决
- 参考错误指南文档
- 应用相应解决方案
- 验证修复效果

### 4. 预防措施
- 更新文档记录
- 改进监控配置
- 制定预防策略

## 性能优化建议

### Nginx 优化

```nginx
# 工作进程数
worker_processes auto;

# 连接数配置
worker_connections 4096;

# 启用 gzip 压缩
gzip on;
gzip_comp_level 6;

# 缓存配置
expires 1d;
add_header Cache-Control "public, immutable";
```

### 数据库优化

```sql
-- 定期清理日志表
DELETE FROM logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 优化表结构
OPTIMIZE TABLE users, tokens, logs;

-- 添加必要索引
CREATE INDEX idx_created_at ON logs(created_at);
```

### 容器优化

```yaml
# 资源限制
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 2G
    reservations:
      cpus: '1.0'
      memory: 1G
```

## 安全最佳实践

### 1. 访问控制
- 使用强密码策略
- 定期更换密钥
- 限制管理员账户数量

### 2. 网络安全
- 配置防火墙规则
- 使用 HTTPS 强制访问
- 启用访问日志监控

### 3. 数据保护
- 定期备份数据
- 加密敏感信息
- 实施数据保留策略

### 4. 系统安全
- 定期更新系统补丁
- 监控异常活动
- 实施入侵检测

## 联系和支持

### 文档更新
- 遇到新问题时及时更新错误指南
- 配置变更后更新部署指南
- 定期审查和完善文档内容

### 技术支持
1. 首先查阅相关文档
2. 检查日志文件和错误信息
3. 尝试常见解决方案
4. 如需进一步支持，请提供详细的错误信息和环境描述

### 贡献指南
- 发现问题时请记录详细信息
- 解决方案验证后更新文档
- 分享最佳实践和优化建议

---

**注意**：本文档会随着项目的发展持续更新，请定期查看最新版本。